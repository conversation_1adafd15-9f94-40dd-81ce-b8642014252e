#!/usr/bin/env bash
set -euo pipefail

echo "🔍 Pre-commit checks..."

# 1. Get list of changed Dart files
CHANGED=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.dart$' || true)

# 2. Auto-format changed Dart files
if [[ -n "$CHANGED" ]]; then
  echo "🎨 Formatting changed files..."
  # Auto-format and add back to index
  fvm dart format $CHANGED >/dev/null
  git add $CHANGED
fi

# 3. Apply automatic lint fixes
echo "🧹 Running dart fix..."
fvm dart fix --apply >/dev/null
git add -u

# 4. Static analysis
# echo "🔬 Flutter analyze..."
# if ! fvm flutter analyze; then
#     echo "❌ Static analysis failed. Please fix the issues above."
#     exit 1
# fi

echo "✅ Pre-commit checks passed!"