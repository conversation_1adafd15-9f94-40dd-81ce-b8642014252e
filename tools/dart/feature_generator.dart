#!/usr/bin/env dart

import 'dart:io';

import 'package:recase/recase.dart';

void main(List<String> arguments) async {
  print('🚀 Flutter Feature Generator');
  print('════════════════════════════');
  print('');

  try {
    // Check if help was requested
    if (arguments.isNotEmpty && arguments.first == '--help') {
      showUsage();
      return;
    }

    // Interactive mode - ask all parameters
    await createFeatureInteractive();
  } catch (e) {
    print('❌ Error: $e');
    exit(1);
  }
}

void showUsage() {
  print('''
🚀 Flutter Feature Generator

Generates new features following clean architecture patterns.

Usage:
  dart tools/dart/feature_generator.dart

The script runs in interactive mode and asks for all necessary parameters.

What gets generated:
  ✅ Domain layer (entities, repositories)
  ✅ Data layer (DTOs, API clients, repository implementations)
  ✅ Presentation layer (controllers, pages)
  ✅ Providers (Riverpod providers)
  ✅ Automatic code generation (build_runner)

Example features:
  📱 user_profile - user profile management
  🛒 product_catalog - product catalog
  📝 task_management - task management
  ⚙️  app_settings - application settings
''');
}

Future<void> createFeatureInteractive() async {
  print("Let's create a new feature! 🎯");
  print('');

  // 1. Feature name
  final featureName = await askFeatureName();

  // 2. Entity name
  final entityName = await askEntityName(featureName);

  // 3. Need API?
  final hasApi = await askYesNo('Do you need API integration?');

  // 4. If API is enabled, always include GET endpoints
  final List<String> endpoints = hasApi ? ['get'] : [];

  // 5. Need controller?
  final hasController = await askYesNo('Do you need a Riverpod controller?');

  // 6. Need page?
  final hasPage = await askYesNo('Do you need a UI page?');

  // Show what will be created
  print('');
  print('📋 Feature configuration:');
  print('  📁 Name: ${featureName.snakeCase}');
  print('  🔧 Entity: ${entityName.snakeCase}');
  print('  🌐 API: ${hasApi ? 'Yes (GET endpoints)' : 'No'}');
  print('  🎮 Controller: ${hasController ? 'Yes' : 'No'}');
  print('  📱 Page: ${hasPage ? 'Yes' : 'No'}');
  print('');

  final confirm = await askYesNo('Create feature with these settings?');
  if (!confirm) {
    print('❌ Creation cancelled');
    return;
  }

  await createFeature(
      featureName, entityName, hasApi, endpoints, hasController, hasPage);
}

Future<String> askFeatureName() async {
  while (true) {
    stdout.write('📝 Enter feature name (snake_case, e.g. user_profile): ');
    final input = stdin.readLineSync()?.trim();

    if (input == null || input.isEmpty) {
      print('❌ Name cannot be empty');
      continue;
    }

    if (!RegExp(r'^[a-z_][a-z0-9_]*$').hasMatch(input)) {
      print('❌ Use only letters, numbers and underscores (snake_case)');
      continue;
    }

    // Check if feature already exists
    final featureDir = Directory('lib/features/${input.snakeCase}');
    if (featureDir.existsSync()) {
      print('❌ Feature "$input" already exists');
      continue;
    }

    return input;
  }
}

Future<String> askEntityName(String featureName) async {
  stdout.write('🏷️  Main entity name (default: ${featureName.snakeCase}): ');
  final input = stdin.readLineSync()?.trim();

  if (input == null || input.isEmpty) {
    return featureName;
  }

  if (!RegExp(r'^[a-z_][a-z0-9_]*$').hasMatch(input)) {
    print('❌ Use only letters, numbers and underscores (snake_case)');
    return askEntityName(featureName);
  }

  return input;
}

Future<bool> askYesNo(String question, {bool defaultValue = true}) async {
  final defaultText = defaultValue ? 'Y/n' : 'y/N';
  stdout.write('❓ $question [$defaultText]: ');
  final input = stdin.readLineSync()?.trim().toLowerCase();

  if (input == null || input.isEmpty) {
    return defaultValue;
  }

  if (input == 'y' || input == 'yes') {
    return true;
  }

  if (input == 'n' || input == 'no') {
    return false;
  }

  print('❌ Enter y/yes or n/no');
  return askYesNo(question, defaultValue: defaultValue);
}

// Removed askEndpoints - API features now always use GET endpoints only

Future<void> createFeature(
  String featureName,
  String entityName,
  bool hasApi,
  List<String> endpoints,
  bool hasController,
  bool hasPage,
) async {
  // Validate feature name
  if (!RegExp(r'^[a-z_][a-z0-9_]*$').hasMatch(featureName)) {
    throw ArgumentError(
        'Feature name must be in snake_case format (letters, numbers, underscores only)');
  }

  // Check if feature already exists
  final featureDir = Directory('lib/features/${featureName.snakeCase}');
  if (featureDir.existsSync()) {
    throw ArgumentError('Feature "$featureName" already exists');
  }

  print('🏗️  Creating feature: ${featureName.snakeCase}');
  print('   Entity: ${entityName.snakeCase}');
  print('   API: ${hasApi ? 'Yes (GET endpoints)' : 'No'}');
  print('   Controller: ${hasController ? 'Yes' : 'No'}');
  print('   Page: ${hasPage ? 'Yes' : 'No'}');

  // Prepare Mason variables
  final vars = <String, dynamic>{
    'feature_name': featureName.snakeCase,
    'entity_name': entityName.snakeCase,
    'has_api': hasApi,
    'api_endpoints': endpoints,
    'has_controller': hasController,
    'has_page': hasPage,
  };

  // Run Mason to generate the feature
  final result = await Process.run(
    'mason',
    ['make', 'feature', '--no-hooks'] +
        vars.entries.map((e) => '--${e.key}=${e.value}').toList(),
    workingDirectory: Directory.current.path,
  );

  if (result.exitCode != 0) {
    print('❌ Failed to create feature');
    print('Mason error: ${result.stderr}');
    exit(1);
  }

  print('✅ Feature "${featureName.snakeCase}" created successfully!');

  // Show next steps
  print('\n📝 Next steps:');
  print('   1. Code generation is already running automatically');
  print('   2. Import your feature in the appropriate router/page');
  if (hasPage) {
    print('   3. Add route for ${featureName.pascalCase}Page');
  }

  // Show file locations
  print('\n📁 Generated files:');
  await _printGeneratedFiles(
      featureName.snakeCase, hasApi, hasController, hasPage);

  // Suggest running build
  print('\n🔨 Running code generation...');
  final buildResult = await Process.run('make', ['build']);

  if (buildResult.exitCode == 0) {
    print('✅ Code generation completed successfully!');
    print('\n🎉 Feature is ready to use!');
  } else {
    print('⚠️  Code generation had issues. Run "make build" manually.');
  }
}

Future<void> _printGeneratedFiles(
    String featureName, bool hasApi, bool hasController, bool hasPage) async {
  final basePath = 'lib/features/$featureName';

  print('   Domain:');
  print('     - $basePath/domain/entities/');
  print('     - $basePath/domain/repositories/');

  print('   Data:');
  print('     - $basePath/data/models/');
  print('     - $basePath/data/repositories/');
  if (hasApi) {
    print('     - $basePath/data/datasources/remote/');
  }

  print('   Presentation:');
  if (hasController) {
    print('     - $basePath/presentation/controllers/');
  }
  if (hasPage) {
    print('     - $basePath/presentation/pages/');
  }

  print('   Providers:');
  print('     - $basePath/providers/');
}
