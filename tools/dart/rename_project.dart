#!/usr/bin/env dart

import 'dart:io';

/// Comprehensive Flutter project rename script
///
/// This script renames a Flutter project by updating all necessary files:
/// - pubspec.yaml
/// - Package imports throughout the codebase
/// - Android build.gradle files (application IDs, app names, namespace)
/// - iOS configuration files (.xcconfig, .pbxproj)
/// - Environment files
/// - Flavor-specific configurations
///
/// Usage:
/// dart tools/dart/rename_project.dart --name <new_name> --organization <new_organization>
///
/// Examples:
/// dart tools/dart/rename_project.dart --name my_awesome_app --organization com.mycompany.myapp
/// dart tools/dart/rename_project.dart --name todo_app --organization io.github.username.todoapp
class ProjectRenamer {
  ProjectRenamer({
    required this.newName,
    required this.newOrganization,
    this.currentName = defaultCurrentName,
    this.currentOrganization = defaultCurrentOrg,
    this.verbose = false,
  });
  static const String defaultCurrentName = 'app';
  static const String defaultCurrentOrg = 'com.example.app';

  final String newName;
  final String newOrganization;
  final String currentName;
  final String currentOrganization;
  final bool verbose;

  Future<void> rename() async {
    print('🚀 Starting project rename...');
    print('Current organization: $currentOrganization');
    print('New organization: $newOrganization');
    print('App name: ${_formatAppName(newName)}');
    print('Note: pubspec.yaml name stays "app" for convenience');
    print('');

    await _validateInputs();

    // Rename operations in order of dependency
    await _renameAndroidConfiguration();
    await _renameIosConfiguration();
    await _renameEnvironmentFiles();
    await _renameFlavorConfigurations();

    print('');
    print('✅ Project rename completed successfully!');
    print('');
    print('Next steps:');
    print('1. Run: fvm flutter clean');
    print('2. Run: fvm flutter pub get');
    print('3. Run: ./builder.sh (to regenerate code)');
    print('4. Test all flavors:');
    print('   - fvm flutter run --flavor local -t lib/main_local.dart');
    print('   - fvm flutter run --flavor dev -t lib/main_dev.dart');
    print('   - fvm flutter run --flavor prod -t lib/main_prod.dart');
    print('5. Commit changes to git');
  }

  Future<void> _validateInputs() async {
    // Validate new name
    if (!RegExp(r'^[a-z][a-z0-9_]*$').hasMatch(newName)) {
      throw ArgumentError(
        'Project name must be lowercase, start with a letter, and contain only letters, numbers, and underscores. Got: "$newName"',
      );
    }

    // Validate organization (allow dots and mixed case for domain names)
    if (!RegExp(r'^[a-zA-Z][a-zA-Z0-9_.]*[a-zA-Z0-9]$')
        .hasMatch(newOrganization)) {
      throw ArgumentError(
        'Organization must start with a letter and contain only letters, numbers, dots, and underscores. Got: "$newOrganization"',
      );
    }

    // Check if we're in a Flutter project
    if (!await File('pubspec.yaml').exists()) {
      throw StateError(
        'pubspec.yaml not found. Are you in a Flutter project root?',
      );
    }

    _log('✓ Input validation passed');
  }

  Future<void> _renameAndroidConfiguration() async {
    _log('📝 Updating Android configuration...');

    await _updateAndroidBuildGradle();
    await _updateAndroidSettingsGradle();
    await _updateAndroidManifest();
    await _updateAndroidKotlinFiles();
    await _renameAndroidDirectories();

    _log('✓ Updated Android configuration');
  }

  Future<void> _updateAndroidBuildGradle() async {
    final buildGradleFile = File('android/app/build.gradle');
    if (!await buildGradleFile.exists()) return;

    var content = await buildGradleFile.readAsString();

    // Update namespace
    content = content.replaceAll(
      'namespace = "$currentOrganization"',
      'namespace = "$newOrganization"',
    );

    // Update default applicationId
    content = content.replaceAll(
      'applicationId = "$currentOrganization"',
      'applicationId = "$newOrganization"',
    );

    // Update flavor application IDs and app names
    final flavors = ['local', 'dev', 'prod'];
    for (final flavor in flavors) {
      // Update application ID
      if (flavor == 'prod') {
        content = content.replaceAll(
          'applicationId "$currentOrganization"',
          'applicationId "$newOrganization"',
        );
      } else {
        content = content.replaceAll(
          'applicationId "$currentOrganization.$flavor"',
          'applicationId "$newOrganization.$flavor"',
        );
      }
    }

    // Update app names for each flavor specifically
    final appName = _formatAppName(newName);

    // Update app names with simple string replacements
    content = content.replaceAll(
      'resValue "string", "app_name", "App Local"',
      'resValue "string", "app_name", "$appName Local"',
    );

    content = content.replaceAll(
      'resValue "string", "app_name", "App Dev"',
      'resValue "string", "app_name", "$appName Dev"',
    );

    // Handle the prod case - replace "App" but not "App Local" or "App Dev"
    content = content.replaceAll(
      RegExp('resValue "string", "app_name", "App"(?! (Local|Dev))'),
      'resValue "string", "app_name", "$appName"',
    );

    await buildGradleFile.writeAsString(content);
  }

  Future<void> _updateAndroidSettingsGradle() async {
    final settingsFile = File('android/settings.gradle');
    if (await settingsFile.exists()) {
      var content = await settingsFile.readAsString();

      // Update project name in settings.gradle if it contains references
      content = content.replaceAll(currentName, newName);

      await settingsFile.writeAsString(content);
    }
  }

  Future<void> _updateAndroidManifest() async {
    final manifestFiles = [
      'android/app/src/main/AndroidManifest.xml',
      'android/app/src/debug/AndroidManifest.xml',
      'android/app/src/profile/AndroidManifest.xml',
    ];

    for (final manifestPath in manifestFiles) {
      final manifestFile = File(manifestPath);
      if (await manifestFile.exists()) {
        var content = await manifestFile.readAsString();
        content = content.replaceAll(currentOrganization, newOrganization);
        await manifestFile.writeAsString(content);
      }
    }
  }

  Future<void> _updateAndroidKotlinFiles() async {
    // Find all Kotlin/Java files in android directory
    final androidDir = Directory('android');
    if (!await androidDir.exists()) return;

    await for (final entity in androidDir.list(recursive: true)) {
      if (entity is File &&
          (entity.path.endsWith('.kt') || entity.path.endsWith('.java'))) {
        var content = await entity.readAsString();
        final originalContent = content;

        // Update package declarations
        content = content.replaceAll(
          'package $currentOrganization',
          'package $newOrganization',
        );

        if (content != originalContent) {
          await entity.writeAsString(content);
          if (verbose) print('  Updated: ${entity.path}');
        }
      }
    }
  }

  Future<void> _renameAndroidDirectories() async {
    // Rename Android package directories
    final currentParts = currentOrganization.split('.');
    final newParts = newOrganization.split('.');

    const androidSrcPath = 'android/app/src/main/kotlin';
    final currentPath = '$androidSrcPath/${currentParts.join('/')}';
    final newPath = '$androidSrcPath/${newParts.join('/')}';

    final currentDir = Directory(currentPath);
    if (await currentDir.exists()) {
      // Create new directory structure
      final newDir = Directory(newPath);
      await newDir.create(recursive: true);

      // Move files from old to new directory
      await for (final entity in currentDir.list()) {
        if (entity is File) {
          final fileName = entity.path.split('/').last;
          final newFilePath = '${newDir.path}/$fileName';
          await entity.copy(newFilePath);
          await entity.delete();
          if (verbose) print('  Moved: ${entity.path} → $newFilePath');
        }
      }

      // Remove old directory structure (if empty)
      await _removeEmptyDirectories(Directory(androidSrcPath), currentParts);
    }
  }

  Future<void> _removeEmptyDirectories(
      Directory baseDir, List<String> pathParts) async {
    for (var i = pathParts.length; i > 0; i--) {
      final partialPath = pathParts.take(i).join('/');
      final dir = Directory('${baseDir.path}/$partialPath');

      if (await dir.exists()) {
        final isEmpty = await dir.list().isEmpty;
        if (isEmpty) {
          await dir.delete();
          if (verbose) print('  Removed empty directory: ${dir.path}');
        } else {
          break; // Stop if directory is not empty
        }
      }
    }
  }

  Future<void> _renameIosConfiguration() async {
    _log('📝 Updating iOS configuration...');

    await _updateIosXcconfig();
    await _updateIosPbxproj();

    _log('✓ Updated iOS configuration');
  }

  Future<void> _updateIosXcconfig() async {
    final xconfigDir = Directory('ios/Flutter');
    if (!await xconfigDir.exists()) return;

    await for (final entity in xconfigDir.list()) {
      if (entity is File && entity.path.endsWith('.xcconfig')) {
        var content = await entity.readAsString();
        final originalContent = content;

        // Update bundle names
        final appName = _formatAppName(newName);
        content = content.replaceAll(
          RegExp('BUNDLE_NAME=.*'),
          'BUNDLE_NAME=$appName',
        );
        content = content.replaceAll(
          RegExp('BUNDLE_DISPLAY_NAME=.*'),
          'BUNDLE_DISPLAY_NAME=$appName',
        );

        // Update flavor-specific bundle names
        if (entity.path.contains('local')) {
          content = content.replaceAll(
            RegExp('BUNDLE_NAME=.*'),
            'BUNDLE_NAME=$appName Local',
          );
          content = content.replaceAll(
            RegExp('BUNDLE_DISPLAY_NAME=.*'),
            'BUNDLE_DISPLAY_NAME=$appName Local',
          );
        } else if (entity.path.contains('dev')) {
          content = content.replaceAll(
            RegExp('BUNDLE_NAME=.*'),
            'BUNDLE_NAME=$appName Dev',
          );
          content = content.replaceAll(
            RegExp('BUNDLE_DISPLAY_NAME=.*'),
            'BUNDLE_DISPLAY_NAME=$appName Dev',
          );
        } else if (entity.path.contains('prod')) {
          content = content.replaceAll(
            RegExp('BUNDLE_NAME=.*'),
            'BUNDLE_NAME=$appName',
          );
          content = content.replaceAll(
            RegExp('BUNDLE_DISPLAY_NAME=.*'),
            'BUNDLE_DISPLAY_NAME=$appName',
          );
        }

        if (content != originalContent) {
          await entity.writeAsString(content);
          if (verbose) print('  Updated: ${entity.path}');
        }
      }
    }
  }

  Future<void> _updateIosPbxproj() async {
    final pbxprojFile = File('ios/Runner.xcodeproj/project.pbxproj');
    if (!await pbxprojFile.exists()) return;

    var content = await pbxprojFile.readAsString();

    // Update all PRODUCT_BUNDLE_IDENTIFIER occurrences
    content = content.replaceAll(
      'PRODUCT_BUNDLE_IDENTIFIER = $currentOrganization;',
      'PRODUCT_BUNDLE_IDENTIFIER = $newOrganization;',
    );
    content = content.replaceAll(
      'PRODUCT_BUNDLE_IDENTIFIER = $currentOrganization.local;',
      'PRODUCT_BUNDLE_IDENTIFIER = $newOrganization.local;',
    );
    content = content.replaceAll(
      'PRODUCT_BUNDLE_IDENTIFIER = $currentOrganization.dev;',
      'PRODUCT_BUNDLE_IDENTIFIER = $newOrganization.dev;',
    );
    content = content.replaceAll(
      'PRODUCT_BUNDLE_IDENTIFIER = $currentOrganization.RunnerTests;',
      'PRODUCT_BUNDLE_IDENTIFIER = $newOrganization.RunnerTests;',
    );

    await pbxprojFile.writeAsString(content);
  }

  Future<void> _renameEnvironmentFiles() async {
    _log('📝 Updating environment files...');

    final envFiles = [
      'assets/.env.example',
      'assets/.env.local',
      'assets/.env.dev',
      'assets/.env.prod',
    ];

    for (final envPath in envFiles) {
      final envFile = File(envPath);
      if (await envFile.exists()) {
        var content = await envFile.readAsString();

        // Update API URLs if they contain the old organization
        content = content.replaceAll(currentOrganization, newOrganization);

        // Update any app-specific URLs
        content = content.replaceAll('/$currentName/', '/$newName/');
        content = content.replaceAll('.$currentName.', '.$newName.');

        await envFile.writeAsString(content);
        if (verbose) print('  Updated: $envPath');
      }
    }

    _log('✓ Updated environment files');
  }

  Future<void> _renameFlavorConfigurations() async {
    _log('📝 Updating flavor configurations...');

    // Update flutter_native_splash configurations if they exist
    final splashConfigFiles = [
      'flutter_native_splash-local.yaml',
      'flutter_native_splash-dev.yaml',
      'flutter_native_splash-prod.yaml',
    ];

    for (final configPath in splashConfigFiles) {
      final configFile = File(configPath);
      if (await configFile.exists()) {
        var content = await configFile.readAsString();

        // Update app name references
        final appName = _formatAppName(newName);
        content = content.replaceAll(
          RegExp(r'app_name:\s*"[^"]*"'),
          'app_name: "$appName"',
        );

        await configFile.writeAsString(content);
        if (verbose) print('  Updated: $configPath');
      }
    }

    _log('✓ Updated flavor configurations');
  }

  String _formatAppName(String name) {
    // Convert snake_case to Title Case
    return name.split('_').map(_capitalizeFirst).join(' ');
  }

  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  void _log(String message) {
    if (verbose) {
      print(message);
    } else {
      // Show only important messages by default
      if (message.startsWith('✓') || message.startsWith('📝')) {
        print(message);
      }
    }
  }
}

void main(List<String> arguments) async {
  String? newName;
  String? newOrganization;
  var currentName = ProjectRenamer.defaultCurrentName;
  var currentOrganization = ProjectRenamer.defaultCurrentOrg;
  var verbose = false;
  var help = false;

  // Parse arguments
  for (var i = 0; i < arguments.length; i++) {
    switch (arguments[i]) {
      case '--name':
      case '-n':
        if (i + 1 < arguments.length) {
          newName = arguments[++i];
        }
      case '--organization':
      case '--org':
      case '-o':
        if (i + 1 < arguments.length) {
          newOrganization = arguments[++i];
        }
      case '--current-name':
        if (i + 1 < arguments.length) {
          currentName = arguments[++i];
        }
      case '--current-organization':
      case '--current-org':
        if (i + 1 < arguments.length) {
          currentOrganization = arguments[++i];
        }
      case '--verbose':
      case '-v':
        verbose = true;
      case '--help':
      case '-h':
        help = true;
      default:
        print('Unknown argument: ${arguments[i]}');
        help = true;
    }
  }

  if (help || newName == null || newOrganization == null) {
    print('Flutter Project Rename Script');
    print('');
    print('Usage:');
    print(
      '  dart tools/dart/rename_project.dart --name <new_name> --organization <new_organization>',
    );
    print('');
    print('Options:');
    print('  --name, -n <name>                 New project name (required)');
    print(
      '  --organization, --org, -o <org>   New organization/bundle ID (required)',
    );
    print(
      '  --current-name <name>             Current project name (default: ${ProjectRenamer.defaultCurrentName})',
    );
    print(
      '  --current-organization <org>      Current organization (default: ${ProjectRenamer.defaultCurrentOrg})',
    );
    print('  --verbose, -v                     Enable verbose output');
    print('  --help, -h                        Show this help message');
    print('');
    print('Examples:');
    print(
      '  dart tools/dart/rename_project.dart -n my_awesome_app -o com.mycompany.myapp',
    );
    print(
      '  dart tools/dart/rename_project.dart -n todo_app -o io.github.username.todoapp',
    );
    print(
      '  dart tools/dart/rename_project.dart -n weather_app -o com.weather.app --verbose',
    );
    print('');
    print('The script will update:');
    print('  • pubspec.yaml');
    print('  • Package imports (package:old_name/ → package:new_name/)');
    print(
      '  • Android configuration (build.gradle, application IDs, app names)',
    );
    print(
      '  • iOS configuration (.xcconfig files, .pbxproj bundle identifiers)',
    );
    print('  • Environment files (.env.*)');
    print('  • Flavor-specific configurations (local, dev, prod)');

    exit(help ? 0 : 1);
  }

  try {
    final renamer = ProjectRenamer(
      newName: newName,
      newOrganization: newOrganization,
      currentName: currentName,
      currentOrganization: currentOrganization,
      verbose: verbose,
    );

    await renamer.rename();
  } catch (e) {
    print('❌ Error: $e');
    exit(1);
  }
}
