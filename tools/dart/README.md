# Project Rename Script

This directory contains the comprehensive project rename script for this Flutter template.

## Usage

### Quick Start (Recommended)
```bash
# Use the convenient shell wrapper
./tools/rename_project.sh -n my_app -o com.mycompany.myapp
```

### Direct Dart Usage (Advanced)
```bash
# Run the Dart script directly for more options
dart tools/dart/rename_project.dart --name my_app --organization com.mycompany.myapp --verbose
```

## What it does

The script automatically updates:

### Core Project Files
- **Note**: `pubspec.yaml` name stays "app" for convenience
- **Note**: Package imports stay `package:app/` for convenience

### Android Configuration
- `android/app/build.gradle` - Application IDs, app names, namespace
- `android/settings.gradle` - Project references 
- `android/app/src/*/AndroidManifest.xml` - Package references
- `android/app/src/main/kotlin/**/*.kt` - Kotlin package declarations
- `android/app/src/main/java/**/*.java` - Java package declarations
- **Directory structure**: Renames and moves `com/example/app/` → `com/organization/app/`
- Flavor-specific application IDs:
  - `prod`: `com.organization.app`
  - `dev`: `com.organization.app.dev`
  - `local`: `com.organization.app.local`
- Flavor-specific app names:
  - `prod`: "App Name"
  - `dev`: "App Name Dev"
  - `local`: "App Name Local"

### iOS Configuration
- `ios/Runner.xcodeproj/project.pbxproj` - Bundle identifiers
- `ios/Flutter/*.xcconfig` - Bundle names and display names
- Flavor-specific bundle IDs (same pattern as Android)
- Flavor-specific app names (same pattern as Android)

### Environment Files
- `assets/.env.*` - Any organization-specific URLs
- Environment-specific API endpoints

### Other Files
- `flutter_native_splash-*.yaml` - App names in splash configurations

## Validation

The script validates:
- **Project name**: Must be lowercase, start with letter, contain only letters/numbers/underscores (used for app display name)
- **Organization**: Must start with letter, contain only letters/numbers/dots/underscores
- **Flutter project**: Ensures `pubspec.yaml` exists

## After Running

1. **Clean and regenerate:**
   ```bash
   fvm flutter clean
   fvm flutter pub get
   ./builder.sh
   ```

2. **Test all flavors:**
   ```bash
   fvm flutter run --flavor local -t lib/main_local.dart
   fvm flutter run --flavor dev -t lib/main_dev.dart
   fvm flutter run --flavor prod -t lib/main_prod.dart
   ```

3. **Commit changes:**
   ```bash
   git add .
   git commit -m "refactor: rename project to new_name"
   ```

## Examples

```bash
# E-commerce app
./tools/rename_project.sh -n ecommerce_app -o com.mycompany.ecommerce

# Personal project
./tools/rename_project.sh -n todo_manager -o io.github.username.todo

# Company project
./tools/rename_project.sh -n customer_portal -o com.acmecorp.portal
```