#!/bin/bash

# Add coverage:ignore-file to spider generated files
echo "Adding coverage:ignore-file to spider generated files..."

find lib/gen/assets -name "*.dart" -type f | while read file; do
    if ! grep -q "coverage:ignore-file" "$file"; then
        # Create temp file with coverage:ignore-file at the top
        echo "// coverage:ignore-file" > temp_file
        cat "$file" >> temp_file
        mv temp_file "$file"
        echo "Added coverage:ignore-file to $file"
    fi
done

echo "Spider coverage ignore fix completed!"