#!/bin/bash

# Rename Script Wrapper
# 
# This is a convenience wrapper around the Dart rename script.
# Usage: ./tools/rename_project.sh <new_name> <new_organization>
# 
# Examples:
# ./tools/rename_project.sh my_awesome_app com.mycompany.myapp
# ./tools/rename_project.sh todo_app io.github.username.todoapp

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in a Flutter project
if [ ! -f "pubspec.yaml" ]; then
    print_error "pubspec.yaml not found. Are you in a Flutter project root?"
    exit 1
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            NEW_NAME="$2"
            shift 2
            ;;
        -o|--org|--organization)
            NEW_ORGANIZATION="$2"
            shift 2
            ;;
        -h|--help)
            echo "Flutter Project Rename Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -n, --name <name>                 New project name (required)"
            echo "  -o, --org, --organization <org>   New organization/bundle ID (required)"
            echo "  -h, --help                        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 -n my_awesome_app -o com.mycompany.myapp"
            echo "  $0 --name todo_app --organization io.github.username.todoapp"
            echo ""
            echo "For more options, use the Dart script directly:"
            echo "  dart tools/dart/rename_project.dart --help"
            exit 0
            ;;
        *)
            # Support positional arguments for backward compatibility
            if [ -z "$NEW_NAME" ]; then
                NEW_NAME="$1"
            elif [ -z "$NEW_ORGANIZATION" ]; then
                NEW_ORGANIZATION="$1"
            else
                print_error "Unknown argument: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Check required arguments
if [ -z "$NEW_NAME" ] || [ -z "$NEW_ORGANIZATION" ]; then
    echo "Usage: $0 -n <new_name> -o <new_organization>"
    echo ""
    echo "Examples:"
    echo "  $0 -n my_awesome_app -o com.mycompany.myapp"
    echo "  $0 -n todo_app -o io.github.username.todoapp"
    echo ""
    echo "For more options, use: $0 --help"
    exit 1
fi

echo "🚀 Starting Flutter project rename..."
print_info "App name: $NEW_NAME (display name)"
print_info "New organization: $NEW_ORGANIZATION"
print_info "Note: pubspec.yaml name stays 'app' for convenience"
echo ""

# Check if tools directory exists
if [ ! -d "tools/dart" ]; then
    print_error "tools/dart directory not found. Please ensure the rename script is in tools/dart/rename_project.dart"
    exit 1
fi

# Run the Dart script
print_info "Running rename script..."
if dart tools/dart/rename_project.dart --name "$NEW_NAME" --organization "$NEW_ORGANIZATION" --verbose; then
    echo ""
    print_success "Rename completed successfully!"
    
    echo ""
    print_warning "Next steps:"
    echo "1. Clean and regenerate:"
    echo "   fvm flutter clean"
    echo "   fvm flutter pub get"
    echo "   ./builder.sh"
    echo ""
    echo "2. Test all flavors:"
    echo "   fvm flutter run --flavor local -t lib/main_local.dart"
    echo "   fvm flutter run --flavor dev -t lib/main_dev.dart"
    echo "   fvm flutter run --flavor prod -t lib/main_prod.dart"
    echo ""
    echo "3. Commit your changes:"
    echo "   git add ."
    echo "   git commit -m \"refactor: rename project to $NEW_NAME\""
    echo ""
    
    print_info "All done! 🎉"
else
    print_error "Rename script failed. Check the error messages above."
    exit 1
fi