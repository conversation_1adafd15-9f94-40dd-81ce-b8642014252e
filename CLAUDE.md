# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Development Commands

**Use FVM for all Flutter commands**: All Flutter commands must use `fvm flutter` instead of `flutter` directly.

### Primary Development Interface (Makefile)
```bash
make start       # Quick start: install deps, generate code, run app
make run         # Run app in local flavor
make test        # Run all tests
make coverage    # Run tests with coverage + open HTML report
make build       # Generate all code (freezed, json, assets, translations)
```

### Testing Commands
```bash
make test                    # Run all tests
fvm flutter test test/path/to/specific_test.dart  # Run single test file
fvm flutter analyze | grep -E "error •" # Prefered this, to check errors (use it to get well formatted errors (if exists))
```

### Code Generation
```bash
fvm dart run build_runner build --delete-conflicting-outputs  # Generate once
```

## Architecture Overview

### Clean Architecture with Riverpod
- **State Management**: Riverpod 3.0+ with code generation (`@riverpod` and `@Riverpod(keepAlive: true)` annotations)
- **Architecture Pattern**: Clean Architecture (domain/data/presentation layers)
- **Feature Structure**: Each feature in `lib/features/` follows clean architecture
- **Dependency Injection**: Riverpod providers throughout the app

### Multi-Flavor Configuration
- **local**: `lib/main_local.dart` - Local development
- **dev**: `lib/main_dev.dart` - Development environment  
- **prod**: `lib/main_prod.dart` - Production environment
- Environment configs in `assets/.env.*` files

### Core Services
- **HTTP Client**: Dio with comprehensive interceptor chain
- **Storage**: Dual system (SharedPreferences + FlutterSecureStorage)
- **Logging**: Talker integration throughout
- **Connectivity**: Real-time network monitoring with visual indicators

## Code Generation Stack
- **build_runner**: Freezed, JSON serialization, Riverpod providers
- **slang**: Type-safe translations (`assets/localizations/`)
- **spider**: Asset reference generation
- **Mason**: Feature scaffolding with bricks

## Development Patterns

### Provider Pattern
```dart

/// Stateful logic, use Notifier
@riverpod
class FeatureNotifier extends _$FeatureNotifier {
  @override
  FeatureState build() => FeatureState.initial(); // Initial state 

  Future<void> doSomething() async {
    state = state.copyWith(isBusy: true);
  }
}

/// Stateless disposable resource (e.g. service / repository) that *should* be destroyed when no longer used
@riverpod
Feature featureProvider(Ref ref) {
  return Feature();
}

/// Long-lived singleton, kept in memory for the whole app-life (e.g. analytics, secure storage) → add keepAlive
@Riverpod(keepAlive: true)
FeatureSingleton featureSingleton(Ref ref) {
  return FeatureSingleton();
}
```

### Feature Generation
Use Mason brick for new features:
```bash
fvm dart run tools/dart/feature_generator.dart  # Interactive feature creation
```

### Storage Services
- Use `SharedKeyValueStore` for general data (ref.shared)
- Use `SecureKeyValueStore` for sensitive data (ref.secured)
- Migration system available for schema updates

````markdown
# Riverpod 3.x - one-page cheat sheet (ESSENTIALS)

| Goal | Minimal snippet | Why it works |
|------|-----------------|--------------|
| **Keep a singleton alive** (e.g. Dio, repositories) | ```dart @Riverpod(keepAlive: true) Dio dio(Ref ref) => Dio(); ``` | `keepAlive: true` turns off auto-dispose for this provider. |
| **Cache an expensive async load** once | ```dart final data = await api.fetch(); ref.keepAlive(); ``` | `ref.keepAlive()` keeps the provider alive; returns a link you can later `close()`. |
| **State survives while the screen is visible** | ```dart final post = ref.watch(postProvider(id)); ``` | A live listener (`watch` or `ConsumerWidget`) prevents disposal during navigation. |
| **Handle loading / error in one line** | ```dart value.when(data: ..., error:(e,_)=>..., loading:()=>...); ``` | `AsyncValue.when` branches for success / error / loading. |
| **Test any provider** | ```dart final c = ProviderContainer(); addTearDown(c.dispose); expect(c.read(counterProvider), 0); ``` | Each test gets an isolated container; call `dispose` after the test. |
| **Mock in tests** | ```dart c.overrideWithProvider(apiProvider, fakeApi); ``` | Overrides inject fakes without touching app code. |
| **Avoid rebuilds** | ```dart ref.watch(counter.select((v) => v.isEven)); ``` | `select` listens to only a slice of state. |
| **Don't leak with families** | Keep default auto-dispose on parameterised providers unless you truly need caching. | Prevents one-instance-per-param memory leaks. |

---

## Quick code examples

### 1. FutureProvider with UI handling

```dart
@riverpod
Future<User> user(UserRef ref) async {
  final api = ref.watch(apiProvider); 
  return api.fetchUser(); // throw → AsyncError
}

// or
@riverpod
class UserNotifier extends _$UserNotifier {
  @override
  Future<User> build() async {
    return ref.watch(apiProvider).fetchUser();
  }

  // Example of refresh logic
  Future<void> refresh() => ref.invalidateSelf();
}


class UserView extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return userAsync.when(
      data:  (u) => Text(u.name),
      error: (e, _) => Text('Error: $e'),
      loading:       () => const CircularProgressIndicator(),
    );
  }
}
````

### 2. NotifierProvider for business logic

```dart
@riverpod
class Counter extends _$Counter<int> {
  @override
  int build() => 0;
  void increment() => state++;
}

// Usage in UI:
ElevatedButton(
  onPressed: () => ref.read(counterProvider.notifier).increment(),
  child: const Text('Increment'),
);
```

### 3. Overriding a provider in a unit test

```dart
test('fetchPost returns fake', () async {
  final container = ProviderContainer(overrides: [
    apiProvider.overrideWithValue(FakeApi()),
  ]);
  addTearDown(container.dispose);

  expect(
    container.read(postProvider.future),
    completion(fakePost),
  );
});
```

---

## Provider cheat-sheet (pick the simplest that fits)

| Provider                                     | Use it for                                                        |
| -------------------------------------------- | ----------------------------------------------------------------- |
| **Provider**                                 | Synchronous constants / caches.                                   |
| **FutureProvider / StreamProvider**          | One-off async calls or continuous streams (returns `AsyncValue`). |
| **StateProvider**                            | Simple mutable primitives (`int`, `bool`, etc.).                  |
| **NotifierProvider / AsyncNotifierProvider** | Complex state + methods (recommended for business logic).         |

---

## Key reminders

* **Auto-dispose is the default.** A provider with no listeners for one frame is destroyed.
* Store the `KeepAliveLink` if you might call `link.close()` later to free memory.
* Prefer `ConsumerWidget` (or hooks) over heavy `ref.read` + `setState` patterns.
* Measure before optimising - `select` and manual reads add complexity and often only matter for very large widgets or lists.

## Navigation Architecture (StatefulShellRoute)

### Two-Level Navigation System
The app uses **StatefulShellRoute.indexedStack** with dual navigation layers:

#### 1. Root Navigator (`_rootNavigatorKey`)
- **Purpose**: Full-screen routes without bottom bar
- **Use cases**: Login, modals, onboarding, fullscreen overlays
- **Navigation**: `context.go('/')` or `context.push('/modal-screen')`

#### 2. Tab Navigators (5 separate keys)
- **Purpose**: Tab-based navigation with bottom bar
- **Keys**: `_homeNavigatorKey`, `_searchNavigatorKey`, `_matcherNavigatorKey`, `_pocketsNavigatorKey`, `_businessNavigatorKey`
- **State**: Each tab maintains its own navigation stack and state

### Navigation Patterns

#### Tab Navigation (with bottom bar)
```dart
// Switch tabs
context.go('/home');     // Home tab
context.go('/search');   // Search tab

// Navigate within tab
context.push('/home/<USER>');   // Push in home tab
context.push('/home/<USER>');    // Push in home tab
```

#### Full-Screen Navigation (hide bottom bar)
```dart
// Add to routes with parentNavigatorKey
GoRoute(
  path: '/fullscreen-route',
  parentNavigatorKey: _rootNavigatorKey,  // ← Key point!
  builder: (context, state) => const FullScreenWidget(),
)

// Navigate
context.push('/fullscreen-route');  // Hides bottom bar
```

#### Login Flow
```dart
// To login (no bottom bar)
context.go('/');

// To main app (with bottom bar)  
context.go('/home');
```

### Route Structure
```
/ (login) - Root level, no bottom bar
├── /home - Tab 0 (with bottom bar)
│   ├── /home/<USER>
│   └── /home/<USER>
├── /search - Tab 1 (with bottom bar)
├── /matcher - Tab 2 (with bottom bar)
├── /pockets - Tab 3 (with bottom bar)
├── /business - Tab 4 (with bottom bar)
└── /share-profile - Root level, no bottom bar
```

### Key Benefits
- **State Preservation**: IndexedStack keeps all tab states alive
- **Flexible Navigation**: Same screen can appear in tab or fullscreen
- **Clean Architecture**: Clear separation between modal and tab navigation
- **Deeplink Support**: All routes are properly addressable
- **No Animations**: Natural tab switching without push animations

### Navigation Guidelines
1. **Tab switching**: Use `context.go()` for main routes
2. **Within tab**: Use `context.push()` for stack navigation  
3. **Modal/Fullscreen**: Use `parentNavigatorKey: _rootNavigatorKey`
4. **Back to login**: Use `context.go('/')`
5. **State management**: Riverpod providers work seamlessly across navigation

## Project Structure Key Points

```
lib/
├── core/           # Infrastructure (networking, storage, logging)
├── features/       # Feature modules (clean architecture)
├── common/         # Shared UI components and theming
└── gen/           # Generated code (do not edit manually)
```

## Quality Assurance

### Linting
- **very_good_analysis** rules with custom additions
- **riverpod_lint** for provider best practices
- Auto-fix available via `make format`

### Testing Strategy
- Unit tests for core services and utilities
- Provider tests for state management
- Integration tests for storage and migrations
- Mock generation with Mockito

## Important Notes

- **Always run code generation** after adding new providers or models
- **Use flavors appropriately**: local for development, dev/prod for deployments  
- **Storage bootstrap**: Required for app initialization
- **Translations**: Add to JSON files in `assets/localizations/`, then regenerate
- **Assets**: Use spider-generated references from `lib/gen/assets.gen.dart`

## Custom Widgets

- **FadeNetworkImage**: Cached network image widget with fade animation and loading/error states
- **ConditionalWrapper**: Utility widget for conditionally wrapping child elements in wrapper

## Developer Memories

- **Sealed Class Usage**: `sealed` should be used to prevent missing concrete implementations of mixins. In this specific case, it helps avoid errors like "Missing concrete implementations of 'getter mixin _$ImageConfig'" which can occur when generating code for complex models
- **Riverpod Memory**: В Riverpod используется (Ref ref) для провайдеров с последних версий
- **Comments**: Prefered rare, short, only in hard part of code comments
- **Provider Scope Tip**: Для таких вещей как SelectedCity который по факту используется везде в приложение а не на 1 экране, нужно использовать не @riverpod, а @Riverpod(keepAlive: true)
- **Simple App Warning**: Не усложняй приложение, я изучаю только Riverpod и не хочу сложное, нужно постепенно мне вкатываться, Это приложение будет простое так что пожалуйста попроще, не усложняем конструкции минимум провайдеров все просто и понятно
- **Localization Principle**: Все локализация в проекте сторого через context.t, нельзя обращяться на прямую потому что потом будут проблемы что текста не будут переводиться без перестройки всего дерева
- **Do not use `make run`**: Avoid using the `make run` command, as it may not work as expected

@CODING_CONVENTIONS.md