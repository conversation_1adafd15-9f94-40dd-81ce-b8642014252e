.PHONY: help clean build test coverage run format watch start
.DEFAULT_GOAL := help

# Colors for output
BOLD := \033[1m
RESET := \033[0m
GREEN := \033[32m
BLUE := \033[34m
YELLOW := \033[33m

help: ## Show this help message
	@echo "$(BOLD)Flutter Project Commands$(RESET)"
	@echo ""
	@awk 'BEGIN {FS = ":.*##"; printf "Usage: make $(GREEN)<command>$(RESET)\n\nCommands:\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

clean: ## Clean build artifacts
	@echo "$(BLUE)Cleaning project...$(RESET)"
	@fvm flutter clean
	@rm -rf coverage/

build: ## Generate all code (freezed, json, assets, translations)
	@echo "$(BLUE)Generating code...$(RESET)"
	@(fvm dart run build_runner build -d) & \
	 (fvm dart run slang) & \
	 (fvm dart run spider build) & \
	 wait
	@echo "$(BLUE)Fixing spider coverage...$(RESET)"
	@./tools/fix_spider_coverage.sh
	@echo "$(GREEN)Code generation completed!$(RESET)"

watch: ## Watch for changes and regenerate code
	@echo "$(BLUE)Watching for changes...$(RESET)"
	@(fvm dart run build_runner watch -d) & \
	 (fvm dart run slang watch) & \
	 (fvm dart run spider build --watch) & \
	 wait

test: ## Run all tests
	@echo "$(BLUE)Running tests...$(RESET)"
	@fvm flutter test

coverage: ## Run tests with coverage and open HTML report
	@echo "$(BLUE)Running tests with coverage...$(RESET)"
	@fvm flutter test --coverage
	@if command -v genhtml >/dev/null 2>&1; then \
		genhtml coverage/lcov.info -o coverage/html --title "Flutter Coverage Report" --quiet; \
		open coverage/html/index.html; \
		echo "$(GREEN)Coverage report opened in browser$(RESET)"; \
	else \
		echo "$(YELLOW)genhtml not found. Install with: brew install lcov$(RESET)"; \
	fi

run: ## Run app in local flavor
	@echo "$(BLUE)Starting app in local flavor...$(RESET)"
	@fvm flutter run --flavor local -t lib/main_local.dart

format: ## Format code and apply automatic lint fixes
	@echo "$(BLUE)Formatting code and applying fixes...$(RESET)"
	@fvm dart fix --apply .
	@fvm dart format lib/ test/

start: ## Quick start: install deps, generate code, and run app
	@fvm flutter pub get && $(MAKE) build && $(MAKE) run