{"errors": {"network": {"server": "Server error, please try again later", "timeout": "Request timeout, please try again later", "connection": "Connection error, please check your internet connection", "parse": "Error while processing data, please try again later", "unknown": "Unknown error, please try again later", "no_internet": "No internet connection. Please check your connection and try again.", "certificate": "Certificate error", "invalid_request": "Invalid request. Please check your input.", "unauthorized": "Please log in to continue.", "forbidden": "You do not have permission to access this resource.", "not_found": "The requested resource was not found.", "internal_server": "Server error. Please try again later.", "connection_timeout": "Connection timeout. Please check your internet connection.", "request_timeout": "Request timeout. Please try again.", "response_timeout": "Response timeout. Please try again.", "unexpected_response": "Unexpected response from server. Please try again.", "cancelled": "Request was cancelled.", "unexpected": "An unexpected error occurred"}, "@network": {"description": "Network and API error messages"}}, "home_screen": {"title": "Flut<PERSON> Starter", "welcome": "App runned in {flavor} flavor", "env_url": "Env API URL: {url}", "buttons": {"test_log": "Test log", "test_network_response": "Test network response", "open_counter": "Open counter", "storage_example": "Storage example", "storage_migration": "Storage Migration Demo", "media_example": "Media example", "network_demo": "Network Demo (Riverpod 3)"}, "language_switcher": {"title": "Language", "english": "English", "russian": "Russian"}}, "connectivity": {"no_internet_title": "No internet connection", "try_again": "Try again"}, "screens": {"business": "Business", "home": "HOME", "login": "<PERSON><PERSON>", "matcher": "Matcher", "pockets": "Pockets", "search": "Search"}, "profile": {"preview_profile": "Preview Profile", "fields": {"name": "Name", "username": "Username", "password": "Password", "email_address": "Email Address", "address": "Address", "my_interests": "My Interests", "my_badges": "My Badges"}, "visibility": {"profile_visibility": "Profile Visibility", "activity_visibility": "Activity Visibility"}, "share_your_profile": "Share Your Profile"}, "settings": {"user_settings": "User Settings", "profile_settings": "Profile Settings", "looking_for_bizsettings": "Looking for Bizsettings?", "app_settings": "App Settings", "notifications": "Notifications", "location": "Location", "tracking": "Tracking", "help": "Help", "faqs": "FAQs", "give_feedback": "<PERSON>", "report_a_bug": "Report a Bug", "invite_a_friend": "Invite a <PERSON>", "legal": "Legal", "terms_of_service": "Terms of Service", "privacy_policy": "Privacy Policy", "sign_out": "Sign out", "version": "Version {version}"}}