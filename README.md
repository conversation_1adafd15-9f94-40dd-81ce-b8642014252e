# Flutter Project

A Flutter application with multi-flavor support and clean architecture.

## Quick Start

### Prerequisites
- **FVM**: Install [Flutter Version Manager](https://fvm.app/)
- **Make**: Pre-installed on macOS/Linux

### Setup
```bash
make start  # Install deps, generate code, run app
```

Create environment files from `.env.example` in `assets/` folder:
- `.env.dev`, `.env.local`, `.env.prod`

## Development Commands

```bash
make help        # Show all available commands
make run         # Run app (local flavor)
make test        # Run all tests
make build       # Generate code (freezed, json, assets)
make format      # Format and lint code
make clean       # Clean build artifacts
```

## Key Technologies

- **Architecture**: Clean Architecture with feature modules
- **Localization**: Slang for type-safe translations
- **Navigation**: GoRouter with StatefulShellRoute
- **Multi-Flavor**: Local/Dev/Prod environments

### ⚠️ State Management
This project relies on **Riverpod 3.x (beta)** for state management.  
The 3.x API is still under active development, so breaking changes may appear.  
They're expected to be minor, but please budget a little time for occasional dependency updates and code tweaks.

## Git Hooks

Install pre-commit hooks for automatic code quality checks:
```bash
./tools/install-hooks.sh
```

## Documentation

- **[Coding Conventions](CODING_CONVENTIONS.md)** - Detailed style guide and patterns

## Project Structure

```
lib/
├── core/           # Infrastructure and services
├── features/       # Feature modules (clean architecture)
├── common/         # Shared UI components and theming
├── shared/         # Global state and utilities
└── gen/            # Generated code (do not edit)
```