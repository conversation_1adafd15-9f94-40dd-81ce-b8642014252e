---
type: "manual"
---

# Coding Conventions Guide

This document provides standardized coding conventions and usage guidelines for the Flutter project. Follow these standards to maintain consistency across the codebase.

## Colors

### Color System Overview
The project uses a comprehensive brand color palette with semantic feedback colors. All colors are defined in `lib/common/theme/palette.dart`.

- Access colors through theme: `context.colors.primary` or `context.xColors.success`
- Use `ExtraColors` extension for custom semantic colors
- Brand quaternary variants include opacity levels (100, 70, 50, 30, 15, 10, 5, 0)

```dart
// ✅ Correct
Container(color: context.colors.primary)
Text('Error', style: TextStyle(color: context.xColors.error))

// ❌ Incorrect
Container(color: Color(0xFFF51010))
```

## Typography

### Text Style Definitions
- **Headings**: XLarge (36px), Large (28px), Medium (20px), Small (18px)
- **Body Text**: Standard (16px), Small (12px), <PERSON> (8px)
- **Line Heights**: 120% for headings, 140% for body text
- **Letter Spacing**: Negative values for better readability (-1px to -0.2px)

### Usage Guidelines
- Always use `context.typo` for accessing text styles
- Map styles to semantic Flutter TextTheme properties
- Avoid creating custom TextStyle objects

```dart
// ✅ Correct
Text('Heading', style: context.typo.headlineLarge)
Text('Body text', style: context.typo.bodyMedium)

// ❌ Incorrect  
Text('Heading', style: AppTextStyle.headingLarge)
Text('Custom', style: TextStyle(fontSize: 20))
```

### Text Style Mapping
- `displayLarge` → XLarge headings (36px)
- `headlineLarge` → Large headings (28px)
- `bodyMedium` → Standard body text (16px)
- `labelSmall` → Tiny text (8px)

## Images

### Image Handling System
The project uses **extended_image** package with custom wrapper widgets for consistent image loading and caching.

```dart
/// Custom cached network image widget with fade animation and loading states.
CachedImage(
  imageUrl,
  width: 100,
  height: 100,
  fit: BoxFit.cover,
  borderRadius: 8,
  fadeDuration: Duration(milliseconds: 300),
)
```

### Asset Management
Generated asset references available in `lib/gen/assets/`:
- **Images**: `lib/gen/assets/images.dart`
- **SVG Icons**: `lib/gen/assets/svg_icons.dart`
- **Resources**: `lib/gen/assets/resources.dart`

### Usage Guidelines
- Use `CachedImage` for all network images
- Access assets through generated references: `Assets.images.profilePng`
- Specify explicit dimensions for optimal caching
- Use SVG icons from `Assets.svgIcons` for scalable graphics

## Routing

### Navigation Architecture
The project uses **GoRouter** with **StatefulShellRoute.indexedStack** for tab-based navigation with state preservation.

### Route Structure
```dart
class Routes {
  static const String root = '/';

  // Main tab routes (with bottom bar)
  static const String home = '/home';
  static const String search = '/search';

  static const String homeSettings = '/home/<USER>';
}
```

### Navigation Patterns

#### Tab Navigation (with bottom bar)
```dart
context.go(Routes.home);
context.push(Routes.homeSettings);
```

#### Full-Screen Navigation (hide bottom bar)
```dart
// Define route with root navigator
GoRoute(
  path: '/modal-screen', // Don't forget to use Routes class
  parentNavigatorKey: _rootNavigatorKey,  // Hides bottom bar
  builder: (context, state) => const ModalScreen(),
)

// Navigate to full-screen
context.push('/modal-screen');
```

### Navigation Keys
- **Root Navigator**: `_rootNavigatorKey` - Full-screen routes
- **Tab Navigators**: Separate keys for each tab (home, search, matcher, pockets, business)

### Guidelines
- Use `context.go()` for tab switching
- Use `context.push()` for stack navigation within tabs
- Use `parentNavigatorKey: _rootNavigatorKey` for modal/full-screen routes
- Route constants defined in `Routes` class - never hardcode paths

## General Coding Rules

### State Management - Riverpod 3.x

#### Provider Patterns
```dart
// Stateful logic with Notifier
@riverpod
class FeatureNotifier extends _$FeatureNotifier {
  @override
  FeatureState build() => FeatureState.initial();
  
  Future<void> doSomething() async {
    state = state.copyWith(isBusy: true);
  }
}

// Disposable resource (auto-dispose)
@riverpod
Feature featureProvider(Ref ref) {
  return Feature();
}

// Long-lived singleton (keepAlive)
@Riverpod(keepAlive: true)
FeatureSingleton featureSingleton(Ref ref) {
  return FeatureSingleton();
}
```

#### Usage Guidelines
- Use `@riverpod` for stateful notifiers and disposable resources
- Use `@Riverpod(keepAlive: true)` for singletons (analytics, storage)
- Access providers with `ref.watch()` in UI and `ref.read()` for one-time operations
- Global state (like SelectedCity) should use `keepAlive: true`

### Localization

#### Translation System
- All text uses **slang** for type-safe translations
- Translation files located in `assets/localizations/`
- **Mandatory**: Use `context.t` for all user-facing text

```dart
// ✅ Correct
Text(context.t.welcome.title)
Text(context.t.errors.networkError)

// ❌ Incorrect - causes rebuild issues
Text(t.welcome.title)
Text('Hardcoded text')
```

### Code Organization

#### Project Structure
```
lib/
├── core/           # Infrastructure (networking, storage, logging)
├── features/       # Feature modules (clean architecture)
├── common/         # Shared UI components and theming
├── shared/         # Global state and utilities
└── gen/           # Generated code (do not edit)
```

#### Naming Conventions
- **Files**: snake_case (e.g., `user_profile_screen.dart`)
- **Classes**: PascalCase (e.g., `UserProfileScreen`)
- **Variables/Functions**: camelCase (e.g., `userName`, `getUserData()`)
- **Constants**: camelCase with descriptive names (e.g., `maxRetryAttempts`)

### Spacing System

Use `Spacing` and `Gap` classes for consistent spacing throughout the app:

```dart
// Spacing values: xxs(6), xs(10), sm(12), md(16), lg(18), xl(20), xxl(24)
Padding(padding: Spacing.scaffoldInsets)  // 20px all sides
Container(padding: Spacing.cardInsets)    // 16px all sides

// Gaps for vertical/horizontal spacing
Column(children: [widget1, Gap.xl, widget2])     // 20px vertical gap
Row(children: [widget1, Gap.hMd, widget2])       // 16px horizontal gap

// Border radius: radiusSm(10), radiusMd(12), radiusLg(16), radiusXl(24)
BorderRadius.circular(Spacing.radiusMd)

// Icon sizes: iconSm(12), iconMd(18), iconLg(24)
Icon(size: Spacing.iconLg)
```

### Helper Widgets

#### ConditionalWrapper
Utility widget for conditionally wrapping child elements.

```dart
ConditionalWrapper(
  condition: shouldWrap,
  onAddWrapper: (child) => Padding(
    padding: EdgeInsets.all(16),
    child: child,
  ),
  child: Text('Content'),
)
```

#### Context Extensions
Convenient extensions for accessing theme and device properties.

```dart
// Theme access
context.theme       // ThemeData
context.typo        // TextTheme
context.colors      // ColorScheme
context.xColors     // ExtraColors

// Device properties
context.width       // Screen width
context.height      // Screen height

// Localization
context.t           // Translations
```

### Code Quality Rules

#### Comments
- **Minimal comments**: Only for complex logic where intent isn't clear
- **English only**: All comments must be in English

#### Development Practices
- Use FVM for all Flutter commands: `fvm flutter run`
- Run code generation after provider/model changes: `make build`
- Format code before commits: `make format` (install hooks from `REAMDE.md`)
- Use generated asset references, never hardcode paths
- Try follow simple clean architecture within features

#### Storage Services
- **Shared data**: Use `ref.watch(sharedStoreProvider)` 
- **Sensitive data**: Use `ref.watch(secureStoreProvider)`
- Bootstrap storage initialization required in main()

### Error Handling
- Use proper exception types from `core/services/network/exceptions/`
- Handle loading states with `AsyncValue.when()`
- Provide meaningful error messages via localization

This guide ensures consistent code style and maintainable architecture across the project. Refer to specific implementation files for detailed examples and keep this document updated with project evolution.