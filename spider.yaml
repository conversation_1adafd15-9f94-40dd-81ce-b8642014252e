# Use: dart run spider build
# For more info on configuration, visit https://birjuvachhani.github.io/spider/grouping/  
# Generates unit tests to verify that the assets exists in assets directory
generate_tests: true

# Use this to remove vcs noise created by the `generated` comments in dart code
no_comments: true

# Exports all the generated file as the one library
export: true

# This allows you to import all the generated references with 1 single import!
use_part_of: true

# Generates a variable that contains a list of all asset values.
use_references_list: true

# Generates files with given ignore rules for file.
ignored_rules:
  - unused_import
  - directives_ordering

# Generates dart font family references for fonts specified in pubspec.yaml
# fonts: true
# -------- OR --------
# fonts:
#   class_name: MyFonts
#   file_name: my_fonts

# Location where all the generated references will be stored
package: gen/assets

groups:
  - path: assets/images
    class_name: Images
    types: [ .png, .jpg, .jpeg, .webp, .webm, .bmp ]
  - path: assets/icons
    class_name: SvgIcons
    types: [ .svg ]