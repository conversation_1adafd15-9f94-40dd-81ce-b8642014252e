import 'package:flutter/material.dart';

abstract class AppPalette {
  // Feedback Colors
  static const Color feedbackPositive = Color(0xFF008701);
  static const Color feedbackNeutral = Color(0xFFF9A717);
  static const Color feedbackNegative = Color(0xFFF51010);

  // Brand Colors - Primary
  static const Color brandPrimary100 = Color(0xFFF51010);
  static const Color brandPrimary50 = Color(0x80F51010);
  static const Color brandPrimary20 = Color(0xCCF51010);
  static const Color brandPrimary10 = Color(0xE6F51010);
  static const Color brandPrimary0 = Color(0xFFF51010);

  // Brand Colors - Secondary
  static const Color brandSecondary100 = Color(0xFFAE0605);

  // Brand Colors - Tertiary
  static const Color brandTertiary100 = Color(0xFF52100F);

  // Brand Colors - Quaternary
  static const Color brandQuaternary100 = Color(0xFF2B0101);
  static const Color brandQuaternary70 = Color(0x4D2B0101);
  static const Color brandQuaternary50 = Color(0x802B0101);
  static const Color brandQuaternary30 = Color(0xB32B0101);
  static const Color brandQuaternary15 = Color(0xD92B0101);
  static const Color brandQuaternary10 = Color(0xE62B0101);
  static const Color brandQuaternary5 = Color(0xF32B0101);
  static const Color brandQuaternary0 = Color(0xFF2B0101);

  // Brand Colors - Quinary
  static const Color brandQuinary100 = Color(0xFFFEAC08);

  // Brand Colors - Senary
  static const Color brandSenary100 = Color(0xFFF2F2F2);
  static const Color brandSenary70 = Color(0x4DF2F2F2);
  static const Color brandSenary50 = Color(0x80F2F2F2);
  static const Color brandSenary20 = Color(0xCCF2F2F2);
  static const Color brandSenary10 = Color(0xE6F2F2F2);
  static const Color brandSenary0 = Color(0xFFF2F2F2);

  // iOS System Colors
  static const Color overlayDefault = Color(0xCC000000);
  static const Color overlayActivityView = Color(0xE0000000);
  static const Color miscSeparator = Color(0x73808080);
  static const Color barBorder = Color(0xB3000000);
  static const Color buttonDisabledBg = Color(0xE0767680);
  static const Color buttonTintedFill = Color(0xD9007AFF);
  static const Color floatingTabTextSelected = Color(0xFF007AFF);
  static const Color floatingTabTextUnselected = Color(0xFF090909);
  static const Color floatingTabPillShadow = Color(0xEB000000);
  static const Color floatingTabPillFill = Color(0xFFFFFFFF);
  static const Color keyboardAccessorySelection = Color(0xFFEBEDF0);
  static const Color keyboardEmojiMic = Color(0x471B1F26);
  static const Color menuLargeSeparator = Color(0xEB000000);
  static const Color sidebarFillSelected = Color(0xFFFFFFFF);
  static const Color sidebarTextSelected = Color(0xFF007AFF);
  static const Color sidebarShadowDragOver = Color(0xCC000000);

  static const Color primary = brandPrimary100;

  static const Color white = Color(0xFFFFFFFF);
}
