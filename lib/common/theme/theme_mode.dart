import 'package:flutter/material.dart';

enum AppThemeMode {
  automatic('automatic'),
  light('light');

  const AppThemeMode(this.value);

  final String value;

  static AppThemeMode fromString(String value) {
    return AppThemeMode.values.firstWhere(
      (mode) => mode.value == value,
      orElse: () => AppThemeMode.automatic,
    );
  }

  ThemeMode get flutterThemeMode {
    switch (this) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.automatic:
        return ThemeMode.system;
    }
  }
}
