import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';

@immutable
class ExtraColors extends ThemeExtension<ExtraColors> {
  const ExtraColors({
    required this.success,
    required this.warning,
    required this.error,
    required this.containerBackground,
    required this.bottomBarBackground,
    // Brand Colors - Primary
    required this.brandPrimary100,
    required this.brandPrimary50,
    required this.brandPrimary20,
    required this.brandPrimary10,
    required this.brandPrimary0,
    // Brand Colors - Secondary
    required this.brandSecondary100,
    // Brand Colors - Tertiary
    required this.brandTertiary100,
    // Brand Colors - Quaternary
    required this.brandQuaternary100,
    required this.brandQuaternary70,
    required this.brandQuaternary50,
    required this.brandQuaternary30,
    required this.brandQuaternary15,
    required this.brandQuaternary10,
    required this.brandQuaternary5,
    required this.brandQuaternary0,
    // Brand Colors - Quinary
    required this.brandQuinary100,
    // Brand Colors - Senary
    required this.brandSenary100,
    required this.brandSenary70,
    required this.brandSenary50,
    required this.brandSenary20,
    required this.brandSenary10,
    required this.brandSenary0,
  });

  final Color success;
  final Color warning;
  final Color error;
  final Color containerBackground;
  final Color bottomBarBackground;

  // Brand Colors - Primary
  final Color brandPrimary100;
  final Color brandPrimary50;
  final Color brandPrimary20;
  final Color brandPrimary10;
  final Color brandPrimary0;

  // Brand Colors - Secondary
  final Color brandSecondary100;

  // Brand Colors - Tertiary
  final Color brandTertiary100;

  // Brand Colors - Quaternary
  final Color brandQuaternary100;
  final Color brandQuaternary70;
  final Color brandQuaternary50;
  final Color brandQuaternary30;
  final Color brandQuaternary15;
  final Color brandQuaternary10;
  final Color brandQuaternary5;
  final Color brandQuaternary0;

  // Brand Colors - Quinary
  final Color brandQuinary100;

  // Brand Colors - Senary
  final Color brandSenary100;
  final Color brandSenary70;
  final Color brandSenary50;
  final Color brandSenary20;
  final Color brandSenary10;
  final Color brandSenary0;

  static const light = ExtraColors(
    success: AppPalette.feedbackPositive,
    warning: AppPalette.feedbackNeutral,
    error: AppPalette.feedbackNegative,
    containerBackground: AppPalette.white,
    bottomBarBackground: AppPalette.floatingTabPillFill,
    // Brand Colors - Primary
    brandPrimary100: AppPalette.brandPrimary100,
    brandPrimary50: AppPalette.brandPrimary50,
    brandPrimary20: AppPalette.brandPrimary20,
    brandPrimary10: AppPalette.brandPrimary10,
    brandPrimary0: AppPalette.brandPrimary0,
    // Brand Colors - Secondary
    brandSecondary100: AppPalette.brandSecondary100,
    // Brand Colors - Tertiary
    brandTertiary100: AppPalette.brandTertiary100,
    // Brand Colors - Quaternary
    brandQuaternary100: AppPalette.brandQuaternary100,
    brandQuaternary70: AppPalette.brandQuaternary70,
    brandQuaternary50: AppPalette.brandQuaternary50,
    brandQuaternary30: AppPalette.brandQuaternary30,
    brandQuaternary15: AppPalette.brandQuaternary15,
    brandQuaternary10: AppPalette.brandQuaternary10,
    brandQuaternary5: AppPalette.brandQuaternary5,
    brandQuaternary0: AppPalette.brandQuaternary0,
    // Brand Colors - Quinary
    brandQuinary100: AppPalette.brandQuinary100,
    // Brand Colors - Senary
    brandSenary100: AppPalette.brandSenary100,
    brandSenary70: AppPalette.brandSenary70,
    brandSenary50: AppPalette.brandSenary50,
    brandSenary20: AppPalette.brandSenary20,
    brandSenary10: AppPalette.brandSenary10,
    brandSenary0: AppPalette.brandSenary0,
  );

  @override
  ExtraColors copyWith({
    Color? success,
    Color? warning,
    Color? error,
    Color? containerBackground,
    Color? bottomBarBackground,
    // Brand Colors - Primary
    Color? brandPrimary100,
    Color? brandPrimary50,
    Color? brandPrimary20,
    Color? brandPrimary10,
    Color? brandPrimary0,
    // Brand Colors - Secondary
    Color? brandSecondary100,
    // Brand Colors - Tertiary
    Color? brandTertiary100,
    // Brand Colors - Quaternary
    Color? brandQuaternary100,
    Color? brandQuaternary70,
    Color? brandQuaternary50,
    Color? brandQuaternary30,
    Color? brandQuaternary15,
    Color? brandQuaternary10,
    Color? brandQuaternary5,
    Color? brandQuaternary0,
    // Brand Colors - Quinary
    Color? brandQuinary100,
    // Brand Colors - Senary
    Color? brandSenary100,
    Color? brandSenary70,
    Color? brandSenary50,
    Color? brandSenary20,
    Color? brandSenary10,
    Color? brandSenary0,
  }) =>
      ExtraColors(
        success: success ?? this.success,
        warning: warning ?? this.warning,
        error: error ?? this.error,
        containerBackground: containerBackground ?? this.containerBackground,
        bottomBarBackground: bottomBarBackground ?? this.bottomBarBackground,
        // Brand Colors - Primary
        brandPrimary100: brandPrimary100 ?? this.brandPrimary100,
        brandPrimary50: brandPrimary50 ?? this.brandPrimary50,
        brandPrimary20: brandPrimary20 ?? this.brandPrimary20,
        brandPrimary10: brandPrimary10 ?? this.brandPrimary10,
        brandPrimary0: brandPrimary0 ?? this.brandPrimary0,
        // Brand Colors - Secondary
        brandSecondary100: brandSecondary100 ?? this.brandSecondary100,
        // Brand Colors - Tertiary
        brandTertiary100: brandTertiary100 ?? this.brandTertiary100,
        // Brand Colors - Quaternary
        brandQuaternary100: brandQuaternary100 ?? this.brandQuaternary100,
        brandQuaternary70: brandQuaternary70 ?? this.brandQuaternary70,
        brandQuaternary50: brandQuaternary50 ?? this.brandQuaternary50,
        brandQuaternary30: brandQuaternary30 ?? this.brandQuaternary30,
        brandQuaternary15: brandQuaternary15 ?? this.brandQuaternary15,
        brandQuaternary10: brandQuaternary10 ?? this.brandQuaternary10,
        brandQuaternary5: brandQuaternary5 ?? this.brandQuaternary5,
        brandQuaternary0: brandQuaternary0 ?? this.brandQuaternary0,
        // Brand Colors - Quinary
        brandQuinary100: brandQuinary100 ?? this.brandQuinary100,
        // Brand Colors - Senary
        brandSenary100: brandSenary100 ?? this.brandSenary100,
        brandSenary70: brandSenary70 ?? this.brandSenary70,
        brandSenary50: brandSenary50 ?? this.brandSenary50,
        brandSenary20: brandSenary20 ?? this.brandSenary20,
        brandSenary10: brandSenary10 ?? this.brandSenary10,
        brandSenary0: brandSenary0 ?? this.brandSenary0,
      );

  @override
  ExtraColors lerp(ThemeExtension<ExtraColors>? other, double t) {
    if (other is! ExtraColors) return this;
    return ExtraColors(
      success: Color.lerp(success, other.success, t)!,
      warning: Color.lerp(warning, other.warning, t)!,
      error: Color.lerp(error, other.error, t)!,
      containerBackground:
          Color.lerp(containerBackground, other.containerBackground, t)!,
      bottomBarBackground:
          Color.lerp(bottomBarBackground, other.bottomBarBackground, t)!,
      // Brand Colors - Primary
      brandPrimary100: Color.lerp(brandPrimary100, other.brandPrimary100, t)!,
      brandPrimary50: Color.lerp(brandPrimary50, other.brandPrimary50, t)!,
      brandPrimary20: Color.lerp(brandPrimary20, other.brandPrimary20, t)!,
      brandPrimary10: Color.lerp(brandPrimary10, other.brandPrimary10, t)!,
      brandPrimary0: Color.lerp(brandPrimary0, other.brandPrimary0, t)!,
      // Brand Colors - Secondary
      brandSecondary100:
          Color.lerp(brandSecondary100, other.brandSecondary100, t)!,
      // Brand Colors - Tertiary
      brandTertiary100:
          Color.lerp(brandTertiary100, other.brandTertiary100, t)!,
      // Brand Colors - Quaternary
      brandQuaternary100:
          Color.lerp(brandQuaternary100, other.brandQuaternary100, t)!,
      brandQuaternary70:
          Color.lerp(brandQuaternary70, other.brandQuaternary70, t)!,
      brandQuaternary50:
          Color.lerp(brandQuaternary50, other.brandQuaternary50, t)!,
      brandQuaternary30:
          Color.lerp(brandQuaternary30, other.brandQuaternary30, t)!,
      brandQuaternary15:
          Color.lerp(brandQuaternary15, other.brandQuaternary15, t)!,
      brandQuaternary10:
          Color.lerp(brandQuaternary10, other.brandQuaternary10, t)!,
      brandQuaternary5:
          Color.lerp(brandQuaternary5, other.brandQuaternary5, t)!,
      brandQuaternary0:
          Color.lerp(brandQuaternary0, other.brandQuaternary0, t)!,
      // Brand Colors - Quinary
      brandQuinary100: Color.lerp(brandQuinary100, other.brandQuinary100, t)!,
      // Brand Colors - Senary
      brandSenary100: Color.lerp(brandSenary100, other.brandSenary100, t)!,
      brandSenary70: Color.lerp(brandSenary70, other.brandSenary70, t)!,
      brandSenary50: Color.lerp(brandSenary50, other.brandSenary50, t)!,
      brandSenary20: Color.lerp(brandSenary20, other.brandSenary20, t)!,
      brandSenary10: Color.lerp(brandSenary10, other.brandSenary10, t)!,
      brandSenary0: Color.lerp(brandSenary0, other.brandSenary0, t)!,
    );
  }
}
