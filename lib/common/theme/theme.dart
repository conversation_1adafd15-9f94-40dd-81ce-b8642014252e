import 'dart:io';
import 'package:app/common/theme/extra_colors.dart';
import 'package:app/common/theme/palette.dart';
import 'package:app/common/theme/typo.dart';
import 'package:flutter/material.dart';

class AppTheme {
  static ThemeData get lightTheme {
    final baseTheme = ThemeData.light();
    final textTheme = TextTheme(
      displayLarge: AppTextStyle.headingXLarge.copyWith(color: AppPalette.brandQuaternary100),
      displayMedium: AppTextStyle.headingLarge.copyWith(color: AppPalette.brandQuaternary100),
      displaySmall: AppTextStyle.headingMedium.copyWith(color: AppPalette.brandQuaternary100),
      headlineLarge: AppTextStyle.headingLarge.copyWith(color: AppPalette.brandQuaternary100),
      headlineMedium: AppTextStyle.headingMedium.copyWith(color: AppPalette.brandQuaternary100),
      headlineSmall: AppTextStyle.headingSmall.copyWith(color: AppPalette.brandQuaternary100),
      titleLarge: AppTextStyle.headingMedium.copyWith(color: AppPalette.brandQuaternary100),
      titleMedium: AppTextStyle.headingSmall.copyWith(color: AppPalette.brandQuaternary100),
      titleSmall: AppTextStyle.textStandard.copyWith(color: AppPalette.brandQuaternary100),
      bodyLarge: AppTextStyle.textStandard.copyWith(color: AppPalette.brandQuaternary100),
      bodyMedium: AppTextStyle.textStandard.copyWith(color: AppPalette.brandQuaternary100),
      bodySmall: AppTextStyle.textSmall.copyWith(color: AppPalette.brandQuaternary100),
      labelLarge: AppTextStyle.textSmall.copyWith(color: AppPalette.brandQuaternary100),
      labelMedium: AppTextStyle.textTiny.copyWith(color: AppPalette.brandQuaternary100),
      labelSmall: AppTextStyle.textTiny.copyWith(color: AppPalette.brandQuaternary100),
    );

    return baseTheme.copyWith(
      scaffoldBackgroundColor: AppPalette.brandSenary100,
      primaryColor: AppPalette.brandPrimary100,
      splashFactory: Platform.isIOS ? NoSplash.splashFactory : InkSplash.splashFactory,
      highlightColor: Platform.isIOS ? Colors.transparent : null,
      colorScheme: baseTheme.colorScheme.copyWith(
        primary: AppPalette.brandPrimary100,
        surface: AppPalette.brandSenary100,
        onSurface: AppPalette.brandQuaternary100,
        onPrimary: AppPalette.white,
        brightness: Brightness.light,
      ),
      buttonTheme: baseTheme.buttonTheme.copyWith(
        buttonColor: AppPalette.brandPrimary100,
        textTheme: ButtonTextTheme.primary,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppPalette.brandPrimary100,
          foregroundColor: AppPalette.brandSenary100,
          splashFactory: Platform.isIOS ? NoSplash.splashFactory : InkSplash.splashFactory,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(36),
          ),
        ),
      ),
      appBarTheme: baseTheme.appBarTheme.copyWith(
        backgroundColor: AppPalette.brandSenary100,
        scrolledUnderElevation: 0,
        elevation: 0,
        titleTextStyle: textTheme.titleMedium?.copyWith(
          color: AppPalette.brandQuaternary100,
          fontSize: 18,
        ),
        iconTheme: const IconThemeData(color: AppPalette.brandQuaternary100),
        actionsIconTheme: const IconThemeData(color: AppPalette.brandQuaternary100),
      ),
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: AppPalette.brandQuaternary100,
        selectionColor: AppPalette.brandQuaternary10,
        selectionHandleColor: AppPalette.brandQuaternary100,
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          splashFactory: Platform.isIOS ? NoSplash.splashFactory : InkSplash.splashFactory,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          splashFactory: Platform.isIOS ? NoSplash.splashFactory : InkSplash.splashFactory,
        ),
      ),
      textTheme: textTheme,
      extensions: [ExtraColors.light],
    );
  }
}

// Backward compatibility
final baseTheme = ThemeData.light();
final theme = AppTheme.lightTheme;
