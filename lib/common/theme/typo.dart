import 'package:flutter/widgets.dart';

abstract class AppTextStyle {
  /// appTextSmall figma style properties
  /// fontFamily: Cera Round Pro
  /// fontSize: 12px
  /// height: 120%
  /// fontWeight: 400
  /// letterSpacing: -0.2px
  /// fontStyle: none
  /// decoration: none
  /// Use: context.typo.bodySmall or context.typo.labelLarge
  static const TextStyle textSmall = TextStyle(
    fontFamily: 'Cera Round Pro',
    fontSize: 12,
    height: 1.2,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.2,
  );

  /// appTextStandard figma style properties
  /// fontFamily: Cera Round Pro
  /// fontSize: 16px
  /// height: 140%
  /// fontWeight: 400
  /// letterSpacing: none
  /// fontStyle: none
  /// decoration: none
  /// Use: context.typo.bodyLarge, context.typo.bodyMedium, or context.typo.titleSmall
  static const TextStyle textStandard = TextStyle(
    fontFamily: 'Cera Round Pro',
    fontSize: 16,
    height: 1.4,
    fontWeight: FontWeight.w400,
  );

  /// appTextTiny figma style properties
  /// fontFamily: Cera Round Pro
  /// fontSize: 8px
  /// height: 140%
  /// fontWeight: 500
  /// letterSpacing: -0.2px
  /// fontStyle: none
  /// decoration: none
  /// Use: context.typo.labelMedium or context.typo.labelSmall
  static const TextStyle textTiny = TextStyle(
    fontFamily: 'Cera Round Pro',
    fontSize: 8,
    height: 1.4,
    fontWeight: FontWeight.w500,
    letterSpacing: -0.2,
  );

  /// appHeadingXLarge figma style properties
  /// fontFamily: Cera Round Pro
  /// fontSize: 36px
  /// height: 120%
  /// fontWeight: 400
  /// letterSpacing: -1px
  /// fontStyle: none
  /// decoration: none
  /// Use: context.typo.displayLarge
  static const TextStyle headingXLarge = TextStyle(
    fontFamily: 'Cera Round Pro',
    fontSize: 36,
    height: 1.2,
    fontWeight: FontWeight.w400,
    letterSpacing: -1,
  );

  /// appHeadingLarge figma style properties
  /// fontFamily: Cera Round Pro
  /// fontSize: 28px
  /// height: 120%
  /// fontWeight: 400
  /// letterSpacing: -1px
  /// fontStyle: none
  /// decoration: none
  /// Use: context.typo.displayMedium or context.typo.headlineLarge
  static const TextStyle headingLarge = TextStyle(
    fontFamily: 'Cera Round Pro',
    fontSize: 28,
    height: 1.2,
    fontWeight: FontWeight.w400,
    letterSpacing: -1,
  );

  /// appHeadingMedium figma style properties
  /// fontFamily: Cera Round Pro
  /// fontSize: 20px
  /// height: 120%
  /// fontWeight: 400
  /// letterSpacing: -0.5px
  /// fontStyle: none
  /// decoration: none
  /// Use: context.typo.displaySmall, context.typo.headlineMedium, or context.typo.titleLarge
  static const TextStyle headingMedium = TextStyle(
    fontFamily: 'Cera Round Pro',
    fontSize: 20,
    height: 1.2,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.5,
  );

  /// appHeadingSmall figma style properties
  /// fontFamily: Cera Round Pro
  /// fontSize: 18px
  /// height: 120%
  /// fontWeight: 400
  /// letterSpacing: -0.5px
  /// fontStyle: none
  /// decoration: none
  /// Use: context.typo.headlineSmall or context.typo.titleMedium
  static const TextStyle headingSmall = TextStyle(
    fontFamily: 'Cera Round Pro',
    fontSize: 18,
    height: 1.2,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.5,
  );

}
