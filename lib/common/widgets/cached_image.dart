import 'package:animate_do/animate_do.dart' show FadeIn;
import 'package:app/common/widgets/conditional_wrapper.dart';
import 'package:extended_image/extended_image.dart' show ExtendedImage, ExtendedRawImage, LoadState;
import 'package:flutter/material.dart';

class CachedImage extends StatefulWidget {
  const CachedImage(
    this.url, {
    super.key,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius = 8,
    this.fadeDuration = const Duration(milliseconds: 1),
  });

  final String url;
  final double? width;
  final double? height;
  final BoxFit fit;
  final double borderRadius;
  final Duration fadeDuration;

  @override
  State<CachedImage> createState() => _CachedImageState();
}

class _CachedImageState extends State<CachedImage> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.fadeDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConditionalWrapper(
      condition: true,
      onAddWrapper: (child) => ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: child,
      ),
      child: ExtendedImage.network(
        widget.url,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        cacheWidth: widget.width != null ? (widget.width! * MediaQuery.of(context).devicePixelRatio).round() : null,
        cacheHeight: widget.height != null ? (widget.height! * MediaQuery.of(context).devicePixelRatio).round() : null,
        loadStateChanged: (state) {
          switch (state.extendedImageLoadState) {
            case LoadState.loading:
              _controller.reset();
              return FadeIn(
                duration: widget.fadeDuration,
                delay: const Duration(milliseconds: 500),
                child: SizedBox(
                  width: widget.width,
                  height: widget.height,
                  child: const Center(
                    child: CircularProgressIndicator.adaptive(),
                  ),
                ),
              );
            case LoadState.failed:
              _controller.reset();
              return GestureDetector(
                onTap: () => state.reLoadImage(),
                child: SizedBox(
                  width: widget.width,
                  height: widget.height,
                  child: const Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                  ),
                ),
              );
            case LoadState.completed:
              _controller.forward();
              return FadeTransition(
                opacity: _controller,
                child: ExtendedRawImage(
                  image: state.extendedImageInfo?.image,
                  width: widget.width,
                  height: widget.height,
                  fit: widget.fit,
                ),
              );
          }
        },
      ),
    );
  }
}
