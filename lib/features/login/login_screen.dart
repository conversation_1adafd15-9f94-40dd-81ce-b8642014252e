import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t.screens.login),
      ),
      body: Center(
        child: IconButton(
          onPressed: () {
            context.go(Routes.home);
          },
          icon: const Icon(Icons.home),
        ),
      ),
    );
  }
}
