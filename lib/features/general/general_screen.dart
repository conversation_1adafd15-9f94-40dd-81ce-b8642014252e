import 'package:app/core/globals.dart';
import 'package:app/features/general/widgets/bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

enum MainPageTab {
  home,
  search,
  matcher,
  pockets,
  business,
}

class MainNavigationScaffold extends StatelessWidget {
  const MainNavigationScaffold({
    required this.navigationShell,
    super.key,
  });

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colors.surface,
      body: SafeArea(
        bottom: false,
        child: Column(
          children: [
            Expanded(
              child: navigationShell,
            ),
            CustomBottomNavBar(
              selectedTab: _indexToTab(navigationShell.currentIndex),
              onTabSelected: (selectedTab) {
                final index = _tabToIndex(selectedTab);
                navigationShell.goBranch(index);
              },
            ),
          ],
        ),
      ),
    );
  }

  MainPageTab _indexToTab(int index) {
    switch (index) {
      case 0:
        return MainPageTab.home;
      case 1:
        return MainPageTab.search;
      case 2:
        return MainPageTab.matcher;
      case 3:
        return MainPageTab.pockets;
      case 4:
        return MainPageTab.business;
      default:
        return MainPageTab.home;
    }
  }

  int _tabToIndex(MainPageTab tab) {
    switch (tab) {
      case MainPageTab.home:
        return 0;
      case MainPageTab.search:
        return 1;
      case MainPageTab.matcher:
        return 2;
      case MainPageTab.pockets:
        return 3;
      case MainPageTab.business:
        return 4;
    }
  }
}
