import 'package:app/core/globals.dart';
import 'package:app/features/general/general_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CustomBottomNavBar extends StatelessWidget {
  const CustomBottomNavBar({
    required this.selectedTab,
    required this.onTabSelected,
    super.key,
  });
  final MainPageTab selectedTab;
  final void Function(MainPageTab tab) onTabSelected;

  @override
  Widget build(BuildContext context) {
    final items = [
      (MainPageTab.home, SvgIcons.home),
      (MainPageTab.search, SvgIcons.search),
      (MainPageTab.matcher, SvgIcons.match),
      (MainPageTab.pockets, SvgIcons.pocket),
      (MainPageTab.business, SvgIcons.business),
    ];

    return ColoredBox(
      color: context.xColors.bottomBarBackground,
      child: SafeArea(
        top: false,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(Spacing.radiusXl),
          child: Container(
            color: context.xColors.bottomBarBackground,
            padding: const EdgeInsets.symmetric(vertical: Spacing.sm),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: items.map((item) {
                final isSelected = item.$1 == selectedTab;
                return GestureDetector(
                  onTap: () => onTabSelected(item.$1),
                  child: Container(
                    padding: Spacing.iconInsets,
                    decoration: isSelected
                        ? BoxDecoration(
                            color: context.colors.primary.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          )
                        : null,
                    child: SvgPicture.asset(
                      item.$2,
                      width: Spacing.iconLg,
                      height: Spacing.iconLg,
                      colorFilter: ColorFilter.mode(
                        isSelected ? context.colors.primary : context.colors.onSurface,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}
