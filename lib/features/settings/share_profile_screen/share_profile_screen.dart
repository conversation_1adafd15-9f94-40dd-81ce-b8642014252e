import 'dart:ui';

import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class ShareProfileScreen extends StatelessWidget {
  const ShareProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          context.go(Routes.homeSettings);
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            context.t.profile.share_your_profile,
            style: context.typo.bodyLarge
                ?.copyWith(color: context.colors.onPrimary),
          ),
          centerTitle: true,
          iconTheme: const IconThemeData(color: Colors.white),
        ),
        body: Stack(
          fit: StackFit.expand,
          children: [
            ClipRect(
              child: Transform.scale(
                scale: 1.1,
                child: ImageFiltered(
                  imageFilter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                  child: Image.asset(
                    Images.profile,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            Container(
              color: Colors.black.withValues(alpha: 0.3),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(100)),
                  child: Image.asset(
                    Images.profile,
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  '@pocketchange',
                  style:
                      context.typo.bodySmall?.copyWith(color: AppPalette.white),
                ),
                const SizedBox(height: 10),
                Text(
                  'Jullian Phyllis',
                  style: context.typo.displayLarge
                      ?.copyWith(color: AppPalette.white),
                ),
              ],
            ),
            SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          color:
                              AppPalette.brandSenary20.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          SvgIcons.share,
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(
                            AppPalette.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                      const SizedBox(width: 30),
                      Container(
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          color:
                              AppPalette.brandSenary20.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          SvgIcons.copy,
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(
                            AppPalette.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                      const SizedBox(width: 30),
                      Container(
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          color:
                              AppPalette.brandSenary20.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: SvgPicture.asset(
                          SvgIcons.mail,
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(
                            AppPalette.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
