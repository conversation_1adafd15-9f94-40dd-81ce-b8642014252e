import 'package:app/core/globals.dart';
import 'package:app/features/settings/widgets/divider_line.dart';
import 'package:app/features/settings/widgets/setting_container.dart';
import 'package:app/features/settings/widgets/setting_item_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colors.surface,
      body: SingleChildScrollView(
        child: Padding(
          padding: Spacing.scaffoldInsets,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    context.t.settings.user_settings,
                    style: context.typo.displayLarge,
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: context.colors.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.asset(
                      SvgIcons.settings,
                      width: 24,
                      height: 24,
                      colorFilter: const ColorFilter.mode(
                        AppPalette.brandPrimary100,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              SettingContainer(
                child: Column(
                  children: [
                    Gap.lg,
                    Padding(
                      padding:
                          const EdgeInsets.symmetric(horizontal: Spacing.lg),
                      child: Row(
                        children: [
                          ClipRRect(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(50)),
                            child: Image.asset(
                              Images.profile,
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Gap.hSm,
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Jillian Philis',
                                style: context.typo.titleLarge,
                              ),
                              Text(
                                '@pocketchange',
                                style: context.typo.bodySmall?.copyWith(
                                  color: context.xColors.brandQuaternary50,
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          InkWell(
                            onTap: () => context.push(Routes.shareProfile),
                            child: Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: context.colors.onSurface
                                    .withValues(alpha: 0.05),
                                shape: BoxShape.circle,
                              ),
                              child: SvgPicture.asset(
                                SvgIcons.share,
                                width: 18,
                                height: 18,
                                colorFilter: ColorFilter.mode(
                                  context.colors.onSurface,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Gap.lg,
                    const DividerLine(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.profile_settings,
                        onTap: () => context.go(Routes.homeProfile),
                      ),
                    ),
                  ],
                ),
              ),
              Gap.xl,
              GestureDetector(
                onTap: () {},
                child: Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Color(
                          0xFF52100E,
                        ), // TODO(stanislavlysenko): replace with theme color when adding new buttons
                        AppPalette.brandPrimary100,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Text(
                        context.t.settings.looking_for_bizsettings,
                        style: context.typo.bodyLarge
                            ?.copyWith(color: context.colors.onPrimary),
                      ),
                      const Spacer(),
                      SvgPicture.asset(
                        SvgIcons.arrowRight,
                        width: 12,
                        height: 12,
                        colorFilter: ColorFilter.mode(
                          context.colors.onPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                context.t.settings.app_settings,
                style: context.typo.bodySmall?.copyWith(
                  color: context.colors.onSurface.withValues(alpha: 0.6),
                ),
              ),
              const SizedBox(height: 10),
              SettingContainer(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.notifications,
                        onTap: () {},
                      ),
                    ),
                    const DividerLine(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.location,
                        onTap: () {},
                      ),
                    ),
                    const DividerLine(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.tracking,
                        onTap: () {},
                      ),
                    ),
                  ],
                ),
              ),
              Gap.xl,
              Text(
                context.t.settings.help,
                style: context.typo.bodySmall?.copyWith(
                  color: context.colors.onSurface.withValues(alpha: 0.6),
                ),
              ),
              const SizedBox(height: 10),
              SettingContainer(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.faqs,
                        onTap: () {},
                      ),
                    ),
                    const DividerLine(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.give_feedback,
                        onTap: () {},
                      ),
                    ),
                    const DividerLine(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.report_a_bug,
                        onTap: () {},
                      ),
                    ),
                    const DividerLine(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.invite_a_friend,
                        onTap: () {},
                      ),
                    ),
                  ],
                ),
              ),
              Gap.xl,
              Text(
                context.t.settings.legal,
                style: context.typo.bodySmall?.copyWith(
                  color: context.colors.onSurface.withValues(alpha: 0.6),
                ),
              ),
              const SizedBox(height: 10),
              SettingContainer(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.terms_of_service,
                        onTap: () {},
                      ),
                    ),
                    const DividerLine(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: SettingItemButton(
                        label: context.t.settings.privacy_policy,
                        onTap: () {},
                      ),
                    ),
                  ],
                ),
              ),
              Gap.xl,
              GestureDetector(
                onTap: () {},
                child: Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        context.colors.primary,
                        const Color(
                          0xFF52100E,
                        ), // TODO(stanislavlysenko): replace with theme color when adding new buttons
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      context.t.settings.sign_out,
                      style: context.typo.bodyLarge
                          ?.copyWith(color: context.colors.onPrimary),
                    ),
                  ),
                ),
              ),
              Gap.xl,
              if (AppInfo.fullVersion != null)
                Center(
                  child: Text(
                    context.t.settings.version(version: AppInfo.version ?? ''),
                    style: context.typo.bodySmall?.copyWith(
                      color: context.colors.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
