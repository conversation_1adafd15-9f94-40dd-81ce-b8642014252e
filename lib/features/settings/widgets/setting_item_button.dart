import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SettingItemButton extends StatelessWidget {
  const SettingItemButton({
    required this.label,
    required this.onTap,
    super.key,
  });

  final String label;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: Spacing.xs, vertical: Spacing.lg,),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: context.typo.bodyLarge
                  ?.copyWith(color: context.colors.onSurface),
            ),
            const Spacer(),
            SvgPicture.asset(
              SvgIcons.arrowRight,
              width: Spacing.iconSm,
              height: Spacing.iconSm,
              colorFilter: ColorFilter.mode(
                context.colors.onSurface,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
