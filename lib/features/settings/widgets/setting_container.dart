import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';

class SettingContainer extends StatelessWidget {
  const SettingContainer({required this.child, super.key});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: context.xColors.containerBackground,
        borderRadius: const BorderRadius.all(
          Radius.circular(Spacing.radiusSm),
        ),
      ),
      child: child,
    );
  }
}
