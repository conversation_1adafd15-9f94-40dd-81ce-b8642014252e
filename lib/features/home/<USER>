import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Center(
      child: Padding(
        padding: Spacing.scaffoldInsets,
        child: Row(
          children: [
            Text(context.t.screens.home),
            const Spacer(),
            IconButton(
              onPressed: () {
                context.go(Routes.homeSettings);
              },
              icon: const Icon(Icons.settings),
            ),
          ],
        ),
      ),
    );
  }
}
