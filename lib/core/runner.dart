// coverage:ignore-file
import 'dart:async';

import 'package:app/core/app/providers/theme_providers.dart';
import 'package:app/core/app/view.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/services/logger/app_logger.dart' show AppLogger, log;
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:talker_riverpod_logger/talker_riverpod_logger.dart';

/// Main runner class to run the app
class Runner {
  static Future<void> setupApp(Flavor flavor) async {
    /// Set the app flavor and load configuration
    App.config = await AppConfig.create(flavor);

    /// Initialize package info
    await AppInfo.init();

    /// Initialize the logger
    AppLogger.instance;

    /// Initialize the app
    final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

    /// Splash screen configuration
    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

    /// Remove rotation
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  static Future<void> run(Flavor flavor) async {
    /// Set the error handler
    FlutterError.onError = (details) {
      log.talker.handle(
        details.exception,
        details.stack ?? StackTrace.empty,
        details.context?.toDescription(),
      );
    };

    /// Run the app with crash logging
    // coverage:ignore-start
    await runZonedGuarded(
      () async {
        await setupApp(flavor);

        /// Define observers for Riverpod containers
        final riverpodObservers = [
          TalkerRiverpodObserver(
            talker: log.talker,
            settings: const TalkerRiverpodLoggerSettings(
              printProviderDisposed: true,
            ),
          ),
        ];

        /// Bootstrap storage initialization before runApp
        final bootstrap = ProviderContainer(
          observers: riverpodObservers,
        );

        // Wait for storage to be fully initialized
        final secureStore =
            await bootstrap.read(initializedSecureStorageProvider.future);
        final sharedStore =
            await bootstrap.read(initializedSharedStorageProvider.future);

        // Create the root container that includes the already initialized stores
        final rootContainer = ProviderContainer(
          overrides: [
            secureStoreProvider.overrideWithValue(secureStore),
            sharedStoreProvider.overrideWithValue(sharedStore),
          ],
          observers: riverpodObservers,
        );

        // Initialize theme controller with stored preferences
        await rootContainer.read(themeControllerProvider.notifier).initialize();

        // We no longer need the bootstrap container after obtaining the stores
        bootstrap.dispose();

        /// Run the root app with the combined container
        runApp(
          UncontrolledProviderScope(
            container: rootContainer,
            child: const RootApp(),
          ),
        );

        /// Hide the splash screen.
        FlutterNativeSplash.remove();
      },
      (error, stack) => log.talker.handle(error, stack, 'Uncaught zone error'),
    );
    // coverage:ignore-end
  }
}
