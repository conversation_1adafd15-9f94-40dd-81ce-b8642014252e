import 'dart:async';

/// Usage:<br>
/// final debounce = Debounce(delay: const Duration(milliseconds: 300));
///
/// debounce.run(() {
///   // do something
/// });
class Debounce {
  Debounce({this.delay = const Duration(milliseconds: 300)});

  final Duration delay;
  Timer? _timer;

  void run(void Function() action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  void cancel() {
    _timer?.cancel();
  }
}
