import 'dart:async';

/// Usage:<br>
/// triggerService.listen((_) {
///   // do something
/// });
///
/// triggerService.trigger();
///
/// **Don't forget to dispose**
class TriggerService {
  final StreamController<void> _controller = StreamController<void>.broadcast();

  Stream<void> get stream => _controller.stream;

  StreamSubscription<void> Function(
    void Function(void event)? onData, {
    bool? cancelOnError,
    void Function()? onDone,
    Function? onError,
  }) get listen => _controller.stream.listen;

  // Method to trigger the stream
  void trigger() {
    _controller.add(null);
  }

  void dispose() {
    _controller.close();
  }
}
