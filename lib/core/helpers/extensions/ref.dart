import 'package:app/core/services/storage/key_value/key_value.dart';
import 'package:app/core/services/storage/providers/storage_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Convenient extensions for accessing storage through WidgetRef
/// Storage is guaranteed to be initialized after bootstrap in main()
extension StorageX on WidgetRef {
  SecureKeyValueStore get secure => read(secureStoreProvider);
  SharedKeyValueStore get shared => read(sharedStoreProvider);
}
