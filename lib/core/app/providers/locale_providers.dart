import 'package:app/core/globals.dart';
import 'package:app/gen/translations.g.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'locale_providers.g.dart';

/// Controller for managing app locale state
@Riverpod(keepAlive: true)
class LocaleController extends _$LocaleController {
  @override
  AppLocale build() {
    // Load saved locale from storage or default to English
    _loadSavedLocale();

    return LocaleSettings.useDeviceLocaleSync();
  }

  /// Load the saved locale from storage
  Future<void> _loadSavedLocale() async {
    try {
      final sharedStore = ref.read(sharedStoreProvider);
      final savedLocaleCode = await sharedStore.read(SharedStoreKeys.locale);

      if (savedLocaleCode != null) {
        final savedLocale = AppLocale.values.where((locale) => locale.languageCode == savedLocaleCode).firstOrNull;

        if (savedLocale != null) {
          state = savedLocale;
          await LocaleSettings.setLocale(savedLocale);
        }
      }
    } on Exception catch (_) {
      // If storage read fails, continue with default locale
      // Log error if needed, but don't crash the app
    }
  }

  /// Change the app locale and persist the preference
  Future<void> setLocale(AppLocale locale) async {
    state = locale;
    await LocaleSettings.setLocale(locale);

    // Persist the locale preference
    final sharedStore = ref.read(sharedStoreProvider);
    await sharedStore.write(SharedStoreKeys.locale, locale.languageCode);
  }

  /// Check if the given locale is currently selected
  bool isSelected(AppLocale locale) => state == locale;
}
