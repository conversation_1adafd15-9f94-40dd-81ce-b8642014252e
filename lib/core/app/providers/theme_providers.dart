import 'package:app/common/theme/theme_mode.dart';
import 'package:app/core/globals.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'theme_providers.g.dart';

/// Simple theme controller that handles theme state synchronously
@Riverpod(keepAlive: true)
class ThemeController extends _$ThemeController {
  @override
  AppThemeMode build() {
    return AppThemeMode.automatic;
  }

  /// Initialize theme from storage (call during app startup)
  Future<void> initialize() async {
    final sharedStore = ref.read(sharedStoreProvider);
    final savedThemeMode = await sharedStore.read(SharedStoreKeys.themeMode);

    final themeMode = savedThemeMode != null ? AppThemeMode.fromString(savedThemeMode) : AppThemeMode.automatic;

    state = themeMode;
  }

  /// Change the app theme mode and persist the preference
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    state = themeMode;

    final sharedStore = ref.read(sharedStoreProvider);
    await sharedStore.write(SharedStoreKeys.themeMode, themeMode.value);
  }

  /// Check if the given theme mode is currently selected
  bool isSelected(AppThemeMode themeMode) {
    return state == themeMode;
  }
}
