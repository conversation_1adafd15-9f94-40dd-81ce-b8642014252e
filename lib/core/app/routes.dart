import 'package:app/core/services/logger/app_logger.dart';
import 'package:app/features/business/business_screen.dart';
import 'package:app/features/general/general_screen.dart';
import 'package:app/features/home/<USER>';
import 'package:app/features/login/login_screen.dart';
import 'package:app/features/matcher/matcher_screen.dart';
import 'package:app/features/pockets/pockets_screen.dart';
import 'package:app/features/search/search_screen.dart';
import 'package:app/features/settings/profile/profile_screen.dart';
import 'package:app/features/settings/settings_screen.dart';
import 'package:app/features/settings/share_profile_screen/share_profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:talker_flutter/talker_flutter.dart';

/// Routes for the app
class Routes {
  // Root routes
  static const String root = '/';
  static const String login = root;
  static const String shareProfile = '/share-profile';

  // Main tab routes
  static const String home = '/home';
  static const String search = '/search';
  static const String matcher = '/matcher';
  static const String pockets = '/pockets';
  static const String business = '/business';

  // Home routes
  static const String homeSettings = '/home/<USER>';
  static const String homeProfile = '/home/<USER>';

  // Route segments (for GoRoute path definitions)
  static const String _settingsSegment = 'settings';
  static const String _profileSegment = 'profile';
}

final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _homeNavigatorKey = GlobalKey<NavigatorState>();
final _searchNavigatorKey = GlobalKey<NavigatorState>();
final _matcherNavigatorKey = GlobalKey<NavigatorState>();
final _pocketsNavigatorKey = GlobalKey<NavigatorState>();
final _businessNavigatorKey = GlobalKey<NavigatorState>();


/// Router configuration for the app using GoRouter
final routerConfig = GoRouter(
  initialLocation: Routes.home,
  navigatorKey: _rootNavigatorKey,
  observers: [TalkerRouteObserver(log.talker)],
  routes: [
    GoRoute(
      path: Routes.login,
      builder: (context, state) => const LoginScreen(),
    ),
    StatefulShellRoute.indexedStack(
      builder: (context, state, navigationShell) {
        return MainNavigationScaffold(navigationShell: navigationShell);
      },
      branches: [
        StatefulShellBranch(
          navigatorKey: _homeNavigatorKey,
          routes: [
            GoRoute(
              path: Routes.home,
              builder: (context, state) => const HomeScreen(),
              routes: [
                GoRoute(
                  path: Routes._settingsSegment,
                  builder: (context, state) => const SettingsScreen(),
                ),
                GoRoute(
                  path: Routes._profileSegment,
                  builder: (context, state) => const ProfileScreen(),
                ),
              ],
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _searchNavigatorKey,
          routes: [
            GoRoute(
              path: Routes.search,
              builder: (context, state) => const SearchScreen(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _matcherNavigatorKey,
          routes: [
            GoRoute(
              path: Routes.matcher,
              builder: (context, state) => const MatcherScreen(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _pocketsNavigatorKey,
          routes: [
            GoRoute(
              path: Routes.pockets,
              builder: (context, state) => const PocketsScreen(),
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _businessNavigatorKey,
          routes: [
            GoRoute(
              path: Routes.business,
              builder: (context, state) => const BusinessScreen(),
            ),
          ],
        ),
      ],
    ),
    GoRoute(
      path: Routes.shareProfile,
      parentNavigatorKey: _rootNavigatorKey,
      builder: (context, state) => const ShareProfileScreen(),
    ),
  ],
);
