// coverage:ignore-file
import 'package:app/core/app/config.dart';
import 'package:flutter/foundation.dart';

export 'package:app/core/app/config.dart';
export 'package:app/core/services/storage/key_value/key_value.dart' show SecureStoreKeys, SharedStoreKeys;
export 'package:app/core/services/storage/providers/storage_providers.dart';

/// Global app configuration
class App {
  /// Contains the app configuration
  static late final AppConfig config;

  /// Returns true if the app is in debug mode (running locally)
  static bool get isDebug => kDebugMode;

  /// Returns true if development features should be enabled (Local or Dev)
  static bool get isDevelopment => config.flavor.isDevelopment;

  /// Returns current flavor
  static Flavor get flavor => config.flavor;
}
