// coverage:ignore-file
import 'package:app/common/theme/theme.dart';
import 'package:app/core/app/config.dart' show Flavor, appConfigProvider;
import 'package:app/core/app/providers/locale_providers.dart';
import 'package:app/core/app/providers/theme_providers.dart';
import 'package:app/core/app/routes.dart';
import 'package:app/gen/translations.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RootApp extends ConsumerWidget {
  const RootApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocale = ref.watch(localeControllerProvider);
    final currentThemeMode = ref.watch(themeControllerProvider);

    return TranslationProvider(
      child: MaterialApp.router(
        routerConfig: routerConfig,
        locale: currentLocale.flutterLocale,
        theme: AppTheme.lightTheme,
        themeMode: currentThemeMode.flutterThemeMode,
        localizationsDelegates: GlobalMaterialLocalizations.delegates,
        supportedLocales: AppLocaleUtils.supportedLocales,
        debugShowCheckedModeBanner: ref.read(appConfigProvider).flavor != Flavor.prod,
      ),
    );
  }
}
