import 'package:package_info_plus/package_info_plus.dart';

/// Static access to app information without using await
class AppInfo {
  AppInfo._();

  static PackageInfo? _packageInfo;

  /// Initialize package info - call once during app startup
  static Future<void> init() async {
    _packageInfo = await PackageInfo.fromPlatform();
  }

  /// App name
  static String get appName => _packageInfo?.appName ?? '';

  /// Package name
  static String get packageName => _packageInfo?.packageName ?? '';

  /// App version
  static String? get version => _packageInfo?.version;

  /// Build number
  static String? get buildNumber => _packageInfo?.buildNumber;

  /// Full version string (version + build)
  static String? get fullVersion =>
      version != null && buildNumber != null ? '$version+$buildNumber' : null;
}
