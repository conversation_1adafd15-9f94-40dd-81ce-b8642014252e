import 'package:app/core/app/app.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:talker_flutter/talker_flutter.dart' show LogLevel;

part 'config.freezed.dart';
part 'config.g.dart';

/// Provider for accessing the app configuration
@Riverpod(keepAlive: true)
AppConfig appConfig(Ref ref) {
  final config = App.config;
  return config;
}

@freezed
sealed class AppConfig with _$AppConfig {
  const factory AppConfig({
    required Flavor flavor,
    required String apiUrl,
    required Duration connectTimeout,
    required Duration receiveTimeout,
    required Duration sendTimeout,
    required int maxRetries,
    required LogLevel logLevel,
  }) = _AppConfig;

  factory AppConfig.fromJson(Map<String, Object?> json) => _$AppConfigFromJson(json);

  /// Development configuration
  factory AppConfig.dev() => AppConfig(
        flavor: Flavor.dev,
        apiUrl: dotenv.env['API_URL'] ?? 'https://api.dev.example.com',
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        maxRetries: 3,
        logLevel: LogLevel.verbose,
      );

  /// Local configuration
  factory AppConfig.local() => AppConfig(
        flavor: Flavor.local,
        apiUrl: dotenv.env['API_URL'] ?? 'http://localhost:8080',
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 15),
        sendTimeout: const Duration(seconds: 15),
        maxRetries: 2,
        logLevel: LogLevel.debug,
      );

  /// Production configuration
  factory AppConfig.prod() => AppConfig(
        flavor: Flavor.prod,
        apiUrl: dotenv.env['API_URL'] ?? 'https://api.example.com',
        connectTimeout: const Duration(seconds: 15),
        receiveTimeout: const Duration(seconds: 15),
        sendTimeout: const Duration(seconds: 15),
        maxRetries: 3,
        logLevel: LogLevel.error,
      );

  /// Test configuration
  factory AppConfig.test() => const AppConfig(
        flavor: Flavor.test,
        apiUrl: 'https://httpbin.org',
        connectTimeout: Duration(seconds: 10),
        receiveTimeout: Duration(seconds: 30),
        sendTimeout: Duration(seconds: 30),
        maxRetries: 3,
        logLevel: LogLevel.verbose,
      );

  // coverage:ignore-start
  /// Initialize configuration by loading environment variables
  static Future<AppConfig> create(Flavor flavor) async {
    final envFile = switch (flavor) {
      Flavor.dev => '.env.dev',
      Flavor.local => '.env.local',
      Flavor.prod => '.env.prod',
      Flavor.test => '.env.test',
    };

    await dotenv.load(fileName: 'assets/$envFile');

    return switch (flavor) {
      Flavor.dev => AppConfig.dev(),
      Flavor.local => AppConfig.local(),
      Flavor.prod => AppConfig.prod(),
      Flavor.test => AppConfig.test(),
    };
  }
  // coverage:ignore-end
}

enum Flavor {
  local,
  dev,
  prod,
  test;

  bool get isProduction => this == Flavor.prod;
  bool get isDevelopment => this == Flavor.dev || this == Flavor.local;
  bool get isTest => this == Flavor.test;
}

extension AppConfigX on AppConfig {
  /// Convenience getters
  bool get isDev => flavor == Flavor.dev;
  bool get isLocal => flavor == Flavor.local;
  bool get isProd => flavor == Flavor.prod;
  bool get isTest => flavor == Flavor.test;

  /// Check if development features should be enabled
  bool get enableDevFeatures => flavor.isDevelopment;
}
