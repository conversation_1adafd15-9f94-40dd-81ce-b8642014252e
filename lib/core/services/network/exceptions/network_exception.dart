import 'package:app/gen/translations.g.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Base exception class for all domain exceptions
abstract class DomainException implements Exception {
  const DomainException({
    this.message,
    this.errorCode,
  });

  /// Human-readable error message
  final String? message;

  /// Machine-readable error code from API
  final String? errorCode;

  /// Convert to user-friendly message for UI
  String toUserMessage([BuildContext? context]);
}

/// Network-related exceptions
abstract class NetworkException extends DomainException {
  const NetworkException({
    super.message,
    super.errorCode,
  });

  /// Connection timeout exception
  factory NetworkException.connectTimeout() => const ConnectTimeoutException();

  /// Send timeout exception
  factory NetworkException.sendTimeout() => const SendTimeoutException();

  /// Receive timeout exception
  factory NetworkException.receiveTimeout() => const ReceiveTimeoutException();

  /// Bad response with status code
  factory NetworkException.badResponse({int? statusCode}) =>
      BadResponseException(statusCode: statusCode);

  /// Request cancelled
  factory NetworkException.cancelled() => const CancelledRequestException();

  /// Unknown network error
  factory NetworkException.unknown({String? message}) =>
      UnknownNetworkException(message: message);

  /// No internet connection
  factory NetworkException.noConnection() => const NoConnectionException();
}

/// HTTP-specific exceptions
abstract class HttpException extends NetworkException {
  const HttpException({
    this.statusCode,
    super.message,
    super.errorCode,
  });

  final int? statusCode;
}

/// 400 Bad Request
class BadRequestException extends HttpException {
  const BadRequestException({super.errorCode})
      : super(statusCode: 400, message: 'Bad Request');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.invalid_request;
    }
    return 'Invalid request. Please check your input.';
  }
}

/// 401 Unauthorized
class UnauthorizedException extends HttpException {
  const UnauthorizedException({super.errorCode})
      : super(statusCode: 401, message: 'Unauthorized');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.unauthorized;
    }
    return 'Please log in to continue.';
  }
}

/// 403 Forbidden
class ForbiddenException extends HttpException {
  const ForbiddenException({super.errorCode})
      : super(statusCode: 403, message: 'Forbidden');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.forbidden;
    }
    return 'You do not have permission to access this resource.';
  }
}

/// 404 Not Found
class NotFoundException extends HttpException {
  const NotFoundException({super.errorCode})
      : super(statusCode: 404, message: 'Not Found');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.not_found;
    }
    return 'The requested resource was not found.';
  }
}

/// 5xx Server Error
class ServerException extends HttpException {
  const ServerException({
    super.statusCode = 500,
    super.errorCode,
  }) : super(message: 'Server Error');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.internal_server;
    }
    return 'Server error. Please try again later.';
  }
}

/// Connection timeout
class ConnectTimeoutException extends NetworkException {
  const ConnectTimeoutException() : super(message: 'Connection timeout');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.connection_timeout;
    }
    return 'Connection timeout. Please check your internet connection.';
  }
}

/// Send timeout
class SendTimeoutException extends NetworkException {
  const SendTimeoutException() : super(message: 'Send timeout');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.request_timeout;
    }
    return 'Request timeout. Please try again.';
  }
}

/// Receive timeout
class ReceiveTimeoutException extends NetworkException {
  const ReceiveTimeoutException() : super(message: 'Receive timeout');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.response_timeout;
    }
    return 'Response timeout. Please try again.';
  }
}

/// Bad response
class BadResponseException extends NetworkException {
  const BadResponseException({this.statusCode})
      : super(message: 'Bad response');

  final int? statusCode;

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.unexpected_response;
    }
    return 'Unexpected response from server. Please try again.';
  }
}

/// Request cancelled
class CancelledRequestException extends NetworkException {
  const CancelledRequestException() : super(message: 'Request cancelled');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.cancelled;
    }
    return 'Request was cancelled.';
  }
}

/// Unknown network error
class UnknownNetworkException extends NetworkException {
  const UnknownNetworkException({String? message})
      : super(message: message ?? 'Unknown network error');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return message ?? context.t.errors.network.unexpected;
    }
    return message ?? 'Unknown error occurred. Please try again.';
  }
}

/// No internet connection
class NoConnectionException extends NetworkException {
  const NoConnectionException() : super(message: 'No internet connection');

  @override
  String toUserMessage([BuildContext? context]) {
    if (context != null) {
      return context.t.errors.network.no_internet;
    }
    return 'No internet connection. Please check your connection and try again.';
  }
}

/// Extension to map DioException to domain exceptions
extension DioExceptionMapper on DioException {
  DomainException toAppException() {
    switch (type) {
      case DioExceptionType.connectionTimeout:
        return NetworkException.connectTimeout();
      case DioExceptionType.sendTimeout:
        return NetworkException.sendTimeout();
      case DioExceptionType.receiveTimeout:
        return NetworkException.receiveTimeout();
      case DioExceptionType.badResponse:
        final statusCode = response?.statusCode ?? 0;
        final errorCode = switch (response?.data) {
          final Map<String, dynamic> data => data['error_code'] as String?,
          _ => null,
        };

        return switch (statusCode) {
          400 => BadRequestException(errorCode: errorCode),
          401 => UnauthorizedException(errorCode: errorCode),
          403 => ForbiddenException(errorCode: errorCode),
          404 => NotFoundException(errorCode: errorCode),
          >= 500 => ServerException(
              statusCode: statusCode,
              errorCode: errorCode,
            ),
          _ => NetworkException.badResponse(statusCode: statusCode),
        };
      case DioExceptionType.cancel:
        return NetworkException.cancelled();
      case DioExceptionType.unknown:
        return NetworkException.unknown(message: message);
      case DioExceptionType.connectionError:
        return NetworkException.noConnection();
      case DioExceptionType.badCertificate:
        return NetworkException.unknown(message: 'Certificate error');
    }
  }
}

/// Extension for AsyncValue error handling in UI
extension AsyncValueUI on AsyncValue<Object?> {
  void showSnackBarOnError(BuildContext context) {
    whenOrNull(
      error: (error, stackTrace) {
        late String message;
        if (error is DomainException) {
          message = error.toUserMessage(context);
        } else {
          message = context.t.errors.network.unexpected;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message)),
        );
      },
    );
  }
}
