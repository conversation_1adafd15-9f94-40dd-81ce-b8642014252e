import 'package:app/core/app/app.dart';
import 'package:app/core/services/logger/app_logger.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sentry_dio/sentry_dio.dart';
import 'package:talker_dio_logger/talker_dio_logger.dart';

/// Provider for Dio HTTP client
@Riverpod(keepAlive: true)
Dio dio(Ref ref) {
  final config = App.config;

  final dio = Dio(
    BaseOptions(
      baseUrl: config.apiUrl,
      connectTimeout: config.connectTimeout,
      receiveTimeout: config.receiveTimeout,
      sendTimeout: config.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ),
  );

  dio.interceptors.add(TalkerDioLogger(talker: log.talker));

  dio.addSentry();

  return dio;
}
