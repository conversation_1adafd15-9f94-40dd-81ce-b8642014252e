// coverage:ignore-file
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'connectivity_provider.g.dart';

final _logger = Logger();

/// Connectivity provider that watches connectivity changes
@Riverpod(keepAlive: true)
Stream<bool> isConnected(Ref ref) async* {
  final connectivity = Connectivity();

  // Emit initial status
  final initial = await connectivity.checkConnectivity();
  final isInitiallyConnected = !initial.contains(ConnectivityResult.none);
  yield isInitiallyConnected;

  // Listen for connectivity changes with debounce to avoid flicker
  bool? lastEmitted = isInitiallyConnected;
  DateTime? lastChangeTime;

  await for (final results in connectivity.onConnectivityChanged) {
    final isConnected = !results.contains(ConnectivityResult.none);
    final now = DateTime.now();

    // If this is a different state than what we last emitted
    if (isConnected != lastEmitted) {
      lastChangeTime = now;

      // Wait 300ms to see if it stabilizes
      await Future<void>.delayed(const Duration(milliseconds: 300));

      // Check if we're still in the same state and enough time has passed
      if (now.difference(lastChangeTime).inMilliseconds >= 300) {
        final currentResults = await connectivity.checkConnectivity();
        final currentConnected = !currentResults.contains(ConnectivityResult.none);

        // Only emit if the state is still consistent
        if (currentConnected == isConnected && currentConnected != lastEmitted) {
          _logger.i('Connectivity changed (debounced): $currentConnected');
          lastEmitted = currentConnected;
          yield currentConnected;
        }
      }
    }
  }
}

/// Provider for connectivity results
@Riverpod(keepAlive: true)
Stream<List<ConnectivityResult>> connectivityResults(Ref ref) {
  return Connectivity().onConnectivityChanged;
}

/// Check current connectivity status
Future<bool> checkConnectivity() async {
  final results = await Connectivity().checkConnectivity();
  return !results.contains(ConnectivityResult.none);
}
