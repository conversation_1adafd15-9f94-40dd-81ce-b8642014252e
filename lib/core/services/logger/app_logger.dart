import 'package:app/core/app/app.dart';
import 'package:logger/logger.dart';
import 'package:talker/talker.dart';

/// Global setter for the [AppLogger] instance.
/// Just call this method once in the app's main method.
AppLogger get log => AppLogger.instance;

/// A logger class that wraps the [Logger] class from the `logger` package.
/// Use this class to log messages in the app.
class AppLogger {
  AppLogger._internal() {
    _logger = Talker(
      settings: TalkerSettings(
        useConsoleLogs: App.isDebug,
      ),
      logger: TalkerLogger(
        settings: TalkerLoggerSettings(
          enableColors: false,
          level: App.config.logLevel,
        ),
      ),
    );
  }

  static final AppLogger _instance = AppLogger._internal();
  static AppLogger get instance => _instance;

  late Talker _logger;
  Talker get talker => _logger;

  void debug(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.debug(message, error, stackTrace);
  }

  void info(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.info(message, error, stackTrace);
  }

  void error(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.error(message, error, stackTrace);
  }

  void warning(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.warning(message, error, stackTrace);
  }

  void fatal(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.critical(message, error, stackTrace);
  }
}
