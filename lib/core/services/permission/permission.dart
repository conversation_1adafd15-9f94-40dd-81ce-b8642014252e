// coverage:ignore-file
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

/// A utility service for handling app permissions
///
/// Usage example:
/// ```dart
/// // Request single permission
/// final result = await PermissionService.requestPermission(Permission.camera);
/// if (result.isDeniedForever) {
///   await PermissionService.openSettings();
/// }
///
/// if (result.isGranted) {
///   // Permission granted
/// }
///
/// // Check multiple permissions
/// final hasPermissions = await PermissionService.areAllGranted([
///   Permission.camera,
///   Permission.microphone,
/// ]);
/// ```
class PermissionService {
  /// Request a specific permission from the user
  ///
  /// Returns [PermissionResult] containing grant status and whether permission
  /// was permanently denied
  static Future<PermissionResult> requestPermission(
    Permission permission,
  ) async {
    final status = await permission.request();
    return PermissionResult.fromStatus(status);
  }

  /// Check if a specific permission is already granted
  ///
  /// Returns [PermissionResult] with current permission status
  static Future<PermissionResult> checkPermission(Permission permission) async {
    final status = await permission.status;
    return PermissionResult.fromStatus(status);
  }

  /// Check permission and request it only if not granted
  ///
  /// Returns [PermissionResult] with final permission status
  static Future<PermissionResult> requestIfNeeded(Permission permission) async {
    final status = await checkPermission(permission);

    if (status.isGranted) return status;

    return requestPermission(permission);
  }

  /// Request multiple permissions at once
  ///
  /// Returns map of permissions with their statuses
  static Future<Map<Permission, PermissionStatus>> requestPermissions(
    List<Permission> permissions,
  ) async {
    // coverage:ignore-line
    return permissions.request();
  }

  /// Check if all specified permissions are granted
  ///
  /// Returns true only if all permissions are granted
  static Future<bool> areAllGranted(List<Permission> permissions) async {
    final statuses = await Future.wait(
      permissions.map((permission) => permission.status),
    );
    return statuses.every((status) => status.isGranted);
  }

  /// Open the app settings page where user can manage permissions
  static Future<void> openSettings() async {
    await openAppSettings();
  }
}

/// Represents the result of a permission request
class PermissionResult {
  const PermissionResult({
    required this.isGranted,
    required this.isDeniedForever,
  });

  factory PermissionResult.fromStatus(PermissionStatus status) {
    return PermissionResult(
      isGranted: status.isGranted,
      isDeniedForever: status.isPermanentlyDenied,
    );
  }

  final bool isGranted;
  final bool isDeniedForever;
}

@Riverpod(keepAlive: true)
PermissionService permissionService(Ref ref) {
  return PermissionService();
}
