// coverage:ignore-file
//
// ignore_for_file: void_checks
import 'package:app/core/services/storage/key_value/migrations/shared_migrations.dart';
import 'package:app/core/services/storage/key_value/migrator/key_value_store.dart';
import 'package:app/core/services/storage/key_value/migrator/key_value_store_migrator.dart';
import 'package:app/core/services/storage/key_value/migrator/migration_observer.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Base implementation over [KeyValueStore] for [SharedPreferences]
///
/// You must call [init] before using
class SharedKeyValueStore with KeyValidatorMixin implements KeyValueStore {
  late SharedPreferences _sharedPreferences;

  @override
  Future<void> init() async {
    _sharedPreferences = await SharedPreferences.getInstance();
  }

  @override
  Future<T?> read<T>(TypeStoreKey<T> typedStoreKey) async {
    validateKeyType(typedStoreKey);

    dynamic value;
    if (T == int) {
      value = _sharedPreferences.getInt(typedStoreKey.key);
    } else if (T == String) {
      value = _sharedPreferences.getString(typedStoreKey.key);
    } else if (T == double) {
      value = _sharedPreferences.getDouble(typedStoreKey.key);
    } else if (T == bool) {
      value = _sharedPreferences.getBool(typedStoreKey.key);
    } else if (T == DateTime) {
      final timestamp = _sharedPreferences.getInt(typedStoreKey.key);
      if (timestamp == null) return null;
      value = DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else {
      throw Exception('Unsupported type: $T');
    }

    return value as T?;
  }

  @override
  Future<bool> contains<T>(TypeStoreKey<T> typedStoreKey) async {
    validateKeyType(typedStoreKey);

    return _sharedPreferences.containsKey(typedStoreKey.key);
  }

  @override
  Future<void> write<T>(TypeStoreKey<T> typedStoreKey, T? value) async {
    validateKeyType(typedStoreKey);

    if (value == null) {
      await _sharedPreferences.remove(typedStoreKey.key);
      return;
    }

    if (value is int) {
      await _sharedPreferences.setInt(typedStoreKey.key, value);
    } else if (value is String) {
      await _sharedPreferences.setString(typedStoreKey.key, value);
    } else if (value is double) {
      await _sharedPreferences.setDouble(typedStoreKey.key, value);
    } else if (value is bool) {
      await _sharedPreferences.setBool(typedStoreKey.key, value);
    } else if (value is DateTime) {
      await _sharedPreferences.setInt(
        typedStoreKey.key,
        value.millisecondsSinceEpoch,
      );
    } else {
      throw Exception('Unsupported type: $T');
    }
  }
}

class SharedStoreMigrator extends BaseKeyValueStoreMigrator {
  SharedStoreMigrator({
    required SharedKeyValueStore keyValueStore,
    required MigrationObserverLogger observer,
  }) : super(
          keyValueStore: keyValueStore,
          observer: observer,
          migrations: {
            SharedMigration1(keyValueStore: keyValueStore),
          },
          storageType: KeyValueStorageType.sharedPrefs,
          schemaVersion: 1, // Current schema version
          schemaVersionKey: BaseStoreKeys.storageVersion,
        );
}
