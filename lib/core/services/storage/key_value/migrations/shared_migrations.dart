import 'package:app/core/services/storage/key_value/migrator/key_value_store.dart';
import 'package:app/core/services/storage/key_value/migrator/key_value_store_migrator.dart';

class SharedMigration1 implements KeyValueStoreMigrationLogic {
  SharedMigration1({required KeyValueStore keyValueStore})
      : _keyValueStore = keyValueStore;

  //
  // ignore: unused_field
  final KeyValueStore _keyValueStore;

  @override
  int get schemeVersion => 1;

  @override
  Future<void> migrate() async {
    // do some logic here
  }
}
