import 'package:app/core/helpers/extensions/default.dart';
import 'package:app/core/services/storage/key_value/migrator/key_value_store.dart';
import 'package:app/core/services/storage/key_value/migrator/migration_observer.dart';
import 'package:flutter/foundation.dart';

abstract class KeyValueStoreMigrationLogic {
  int get schemeVersion;

  Future<void> migrate();
}

typedef CreateStoreFunc = Future<void> Function(int createdVersion);

/// Object for migrating [KeyValueStore] stores
/// If a new migration is needed, increase the version [schemaVersion] (N)
/// Implement a new migration to version N - PrefsMigrationN, and add it to the list of migrations
class KeyValueStoreMigrator {
  KeyValueStoreMigrator({
    required this.keyValueStore,
    required this.migrations,
    required this.schemaVersion,
    required this.schemaVersionKey,
    required this.storageType,
    this.observer,
    this.onCreateFunc,
  });

  @protected
  final MigrationObserver? observer;

  @protected
  final KeyValueStore keyValueStore;

  final KeyValueStorageType storageType;

  @protected
  final Set<KeyValueStoreMigrationLogic> migrations;

  @protected
  final CreateStoreFunc? onCreateFunc;

  /// Current version of the store
  final int schemaVersion;

  /// Key for storing the version of the store
  final TypeStoreKey<int> schemaVersionKey;

  KeyValueStorageType get publicStorageType => storageType;
  int get publicSchemaVersion => schemaVersion;

  Future<void> migrate() async {
    final currentVersion = await _readCurrentVersion();

    if (currentVersion == null) {
      // For new store, we consider it starts from version 0
      // All migrations will be applied from 0 to current [schemaVersion]

      const fromVersion = 0;
      await onCreate(schemaVersion);
      await onUpgrade(fromVersion, schemaVersion);
      await _writeNewVersion(schemaVersion);

      return;
    }

    if (currentVersion != schemaVersion) {
      // If version doesn't match, perform migration
      await onUpgrade(currentVersion, schemaVersion);
      await _writeNewVersion(schemaVersion);
    }
  }

  /// Method for creating a key-value store
  Future<void> onCreate(int createdVersion) async {
    await onCreateFunc?.call(createdVersion);
    await observer?.onCreate(storageType, createdVersion);
  }

  /// Method for migrating from version [fromVersion] to [toVersion]
  /// The method sequentially performs migration through a set of migrations
  Future<void> onUpgrade(int fromVersion, int toVersion) async {
    await observer?.onUpgrade(storageType, fromVersion, toVersion);

    var prefsVersion = fromVersion;
    while (prefsVersion < toVersion) {
      prefsVersion++;
      final migartionLogic = migrations.firstWhereOrNull(
        (migrator) => migrator.schemeVersion == prefsVersion,
      );
      if (migartionLogic == null) {
        await observer?.onMissedMigration(storageType, prefsVersion);
        continue;
      } else {
        await migartionLogic.migrate();
        await observer?.onMigratedSingle(
          storageType,
          migartionLogic.schemeVersion,
        );
      }
    }

    await observer?.onMigrated(storageType, fromVersion, toVersion);
  }

  Future<int?> _readCurrentVersion() =>
      keyValueStore.read(schemaVersionKey).then((value) => value);

  Future<void> _writeNewVersion(int newVersion) =>
      keyValueStore.write(schemaVersionKey, newVersion);
}

abstract class BaseKeyValueStoreMigrator extends KeyValueStoreMigrator {
  BaseKeyValueStoreMigrator({
    required super.keyValueStore,
    required super.observer,
    required super.migrations,
    required super.schemaVersion,
    required super.schemaVersionKey,
    required super.storageType,
    super.onCreateFunc,
  });
}
