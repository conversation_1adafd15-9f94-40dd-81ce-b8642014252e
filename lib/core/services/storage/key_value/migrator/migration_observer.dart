import 'package:app/core/services/logger/app_logger.dart';
import 'package:app/core/services/storage/key_value/migrator/key_value_store.dart';

abstract class MigrationObserver {
  Future<void> onCreate(KeyValueStorageType type, int createdVersion);
  Future<void> onMissedMigration(KeyValueStorageType type, int version);
  Future<void> onUpgrade(
    KeyValueStorageType type,
    int fromVersion,
    int toVersion,
  );
  Future<void> onMigratedSingle(KeyValueStorageType type, int version);
  Future<void> onMigrated(
    KeyValueStorageType type,
    int fromVersion,
    int toVersion,
  );
}

class MigrationObserverLogger implements MigrationObserver {
  MigrationObserverLogger();

  String _getKeyValueStorageTypeName(KeyValueStorageType type) {
    switch (type) {
      case KeyValueStorageType.sharedPrefs:
        return 'SharedPreferences';
      case KeyValueStorageType.secureStorage:
        return 'SecureStorage';
    }
  }

  @override
  Future<void> onCreate(KeyValueStorageType type, int createdVersion) async {
    log.info(
      '${_getKeyValueStorageTypeName(type).padRight(17)} | Created   | v$createdVersion',
    );
  }

  @override
  Future<void> onMissedMigration(KeyValueStorageType type, int version) async {
    log.error(
      '${_getKeyValueStorageTypeName(type).padRight(17)} | Migration | Missed for version $version',
    );
  }

  @override
  Future<void> onUpgrade(
    KeyValueStorageType type,
    int fromVersion,
    int toVersion,
  ) async {
    log.info(
      '${_getKeyValueStorageTypeName(type).padRight(17)} | Upgrading | v$fromVersion -> v$toVersion',
    );
  }

  @override
  Future<void> onMigrated(
    KeyValueStorageType type,
    int fromVersion,
    int toVersion,
  ) async {
    log.info(
      '${_getKeyValueStorageTypeName(type).padRight(17)} | Migrated  | v$fromVersion -> v$toVersion',
    );
  }

  @override
  Future<void> onMigratedSingle(KeyValueStorageType type, int version) async {
    log.info(
      '${_getKeyValueStorageTypeName(type).padRight(17)} | Migrated  | v$version',
    );
  }
}
