import 'package:app/core/services/storage/key_value/secure_store.dart';
import 'package:app/core/services/storage/key_value/shared_store.dart';

/// Protocol for a typed key-value data store that works with [TypeStoreKey]
abstract class KeyValueStore {
  /// Method that checks if there is any value stored by the key [typedStoreKey]
  Future<bool> contains<T>(TypeStoreKey<T> typedStoreKey);

  /// Method to initialize the store
  Future<void> init();

  /// Method to read a value by key [typedStore<PERSON>ey], if the value is not found, null is returned
  /// If the value is found in the store, its type is cast to [T]
  Future<T?> read<T>(TypeStoreKey<T> typedStoreKey);

  /// Method for writing a value by key [typedStoreKey], if you need to delete the value, you need to pass null
  Future<void> write<T>(TypeStoreKey<T> typedStoreKey, T? value);
}

/// Mixin for validating key types
mixin KeyValidatorMixin {
  void validateKeyType<T>(TypeStoreKey<T> key) {
    if (key is BaseStore<PERSON><PERSON>) {
      return;
    }

    if (this is SecureKeyValueStore && key is! SecureStoreKey) {
      throw Exception(
        'Invalid key type. SecureStorage can only use SecureStoreKeys. Got: ${key.runtimeType}',
      );
    }

    if (this is SharedKeyValueStore && key is! SharedStoreKey) {
      throw Exception(
        'Invalid key type. SharedPreferences can only use SharedStoreKeys. Got: ${key.runtimeType}',
      );
    }
  }
}

/// Object that is a typed key used in key-value stores for more convenient work with them
/// [T] - the type of the stored value
/// [key] - string key
/// The store can limit the type [T], usually it is limited to standard types: [int], [double], [String], [bool]. For secure storage data saves only in [String] format.
class TypeStoreKey<T> {
  const TypeStoreKey(this.key);

  final String key;

  Type get type => T;

  @override
  String toString() => 'TypeStoreKey(key: $key)';
}

class SecureStoreKey<T> extends TypeStoreKey<T> {
  const SecureStoreKey(super.key);
}

class SharedStoreKey<T> extends TypeStoreKey<T> {
  const SharedStoreKey(super.key);
}

class BaseStoreKey<T> extends TypeStoreKey<T> {
  const BaseStoreKey(super.key);
}

/// Base keys for the store
abstract class BaseStoreKeys {
  static const storageVersion = BaseStoreKey<int>('storage_version');
}

/// Enum for determining the type of storage
enum KeyValueStorageType {
  sharedPrefs,
  secureStorage,
}
