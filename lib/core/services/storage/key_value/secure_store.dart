// coverage:ignore-file
// ignore_for_file: void_checks
import 'package:app/core/services/storage/key_value/migrations/secure_migrations.dart';
import 'package:app/core/services/storage/key_value/migrator/key_value_store.dart';
import 'package:app/core/services/storage/key_value/migrator/key_value_store_migrator.dart';
import 'package:app/core/services/storage/key_value/migrator/migration_observer.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Base implementation over [KeyValueStore] for [FlutterSecureStorage]
///
/// You must call [init] before using
class SecureKeyValueStore with KeyValidatorMixin implements KeyValueStore {
  late FlutterSecureStorage _secureStorage;

  final _getIOSOptions =
      const IOSOptions(accessibility: IOSAccessibility.first_unlock);

  @override
  Future<void> init() async {
    _secureStorage = const FlutterSecureStorage();
  }

  @override
  Future<T?> read<T>(TypeStoreKey<T> typedStoreKey) async {
    validateKeyType(typedStoreKey);

    final value = await _secureStorage.read(
      key: typedStoreKey.key,
      iOptions: _getIOSOptions,
    );

    if (value == null) return null;

    if (T == int) {
      return int.parse(value) as T;
    } else if (T == String) {
      return value as T;
    } else if (T == double) {
      return double.parse(value) as T;
    } else if (T == bool) {
      return (value.toLowerCase() == 'true') as T;
    } else if (T == DateTime) {
      return DateTime.fromMillisecondsSinceEpoch(int.parse(value)) as T;
    }

    throw Exception('Type not supported $T');
  }

  @override
  Future<bool> contains<T>(TypeStoreKey<T> typedStoreKey) async {
    validateKeyType(typedStoreKey);

    return _secureStorage.containsKey(
      key: typedStoreKey.key,
      iOptions: _getIOSOptions,
    );
  }

  @override
  Future<void> write<T>(TypeStoreKey<T> typedStoreKey, T? value) async {
    validateKeyType(typedStoreKey);

    if (value == null) {
      await _secureStorage.delete(key: typedStoreKey.key);
      return;
    }

    String stringValue;
    if (value is DateTime) {
      stringValue = value.millisecondsSinceEpoch.toString();
    } else {
      stringValue = value.toString();
    }

    await _secureStorage.write(
      key: typedStoreKey.key,
      value: stringValue,
    );
  }
}

class SecureStorageMigrator extends BaseKeyValueStoreMigrator {
  SecureStorageMigrator({
    required SecureKeyValueStore keyValueStore,
    required MigrationObserverLogger observer,
  }) : super(
          keyValueStore: keyValueStore,
          observer: observer,
          migrations: {
            SecureStorageMigration1(keyValueStore: keyValueStore),
          },
          storageType: KeyValueStorageType.secureStorage,
          schemaVersion: 1, // Current schema version
          schemaVersionKey: BaseStoreKeys.storageVersion,
        );
}
