// coverage:ignore-file
import 'package:app/core/services/storage/key_value/key_value.dart';
import 'package:app/core/services/storage/key_value/migrator/migration_observer.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'storage_providers.g.dart';

/// Provider for secure storage instance (base, not initialized)
@Riverpod(keepAlive: true)
SecureKeyValueStore _secureKeyValueStore(Ref ref) {
  return SecureKeyValueStore();
}

/// Provider for shared storage instance (base, not initialized)
@Riverpod(keepAlive: true)
SharedKeyValueStore _sharedKeyValueStore(Ref ref) {
  return SharedKeyValueStore();
}

/// Provider for migration observer
@Riverpod(keepAlive: true)
MigrationObserverLogger migrationObserver(Ref ref) {
  return MigrationObserverLogger();
}

/// Provider for secure storage migrator
@Riverpod(keepAlive: true)
SecureStorageMigrator _secureStorageMigrator(Ref ref) {
  return SecureStorageMigrator(
    keyValueStore: ref.watch(_secureKeyValueStoreProvider),
    observer: ref.watch(migrationObserverProvider),
  );
}

/// Provider for shared storage migrator
@Riverpod(keepAlive: true)
SharedStoreMigrator _sharedStoreMigrator(Ref ref) {
  return SharedStoreMigrator(
    keyValueStore: ref.watch(_sharedKeyValueStoreProvider),
    observer: ref.watch(migrationObserverProvider),
  );
}

/// Internal provider for fully initialized secure storage
/// Used only for bootstrap in main() - not for direct UI consumption
@Riverpod(keepAlive: true)
Future<SecureKeyValueStore> _initializedSecureStorage(Ref ref) async {
  final store = ref.watch(_secureKeyValueStoreProvider);
  final migrator = ref.watch(_secureStorageMigratorProvider);

  // Initialize store
  await store.init();

  // Run migrations
  await migrator.migrate();

  return store;
}

/// Internal provider for fully initialized shared storage
/// Used only for bootstrap in main() - not for direct UI consumption
@Riverpod(keepAlive: true)
Future<SharedKeyValueStore> _initializedSharedStorage(Ref ref) async {
  final store = ref.watch(_sharedKeyValueStoreProvider);
  final migrator = ref.watch(_sharedStoreMigratorProvider);

  // Initialize store
  await store.init();

  // Run migrations
  await migrator.migrate();

  return store;
}

/// Public alias for bootstrap usage in main()
const initializedSecureStorageProvider = _initializedSecureStorageProvider;
const initializedSharedStorageProvider = _initializedSharedStorageProvider;

/// Sync provider for secure storage - override in main() after bootstrap
@Riverpod(keepAlive: true)
SecureKeyValueStore secureStore(Ref ref) {
  // coverage:ignore-line
  throw UnimplementedError('secureStoreProvider — override in main()');
}

/// Sync provider for shared storage - override in main() after bootstrap
@Riverpod(keepAlive: true)
SharedKeyValueStore sharedStore(Ref ref) {
  // coverage:ignore-line
  throw UnimplementedError('sharedStoreProvider — override in main()');
}
