import 'package:app/core/app/routes.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/services/logger/app_logger.dart';
import 'package:app/gen/translations.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/storage_test_utils.dart';

export 'package:app/core/app/routes.dart' show Routes;

Widget prepareTestApp({String? initialLocation, ProviderContainer? container}) {
  FlutterError.onError = (FlutterErrorDetails details) {
    // test
    // ignore: only_throw_errors
    throw details.exception;
  };

  /// Used default router configuration, but override the initial location if provided
  final testRounterConfig = GoRouter(
    initialLocation: initialLocation ?? '/',
    routes: routerConfig.configuration.routes,
  );

  final Widget child = TranslationProvider(
    child: MaterialApp.router(
      localizationsDelegates: GlobalMaterialLocalizations.delegates,
      supportedLocales: const [Locale('en')],
      locale: const Locale('en'),
      routerConfig: testRounterConfig,
    ),
  );

  if (container != null) {
    return UncontrolledProviderScope(
      container: container,
      child: child,
    );
  } else {
    return ProviderScope(
      child: child,
    );
  }
}

Future<void> setupAll() async {
  SharedPreferences.setMockInitialValues({});

  // Initialize App.config for tests without loading environment files
  App.config = AppConfig.test();

  // Initialize logger after config is set
  AppLogger.instance;
}

/// Create a properly configured test app with bootstrapped storage
Future<Widget> prepareTestAppWithStorage({String? initialLocation}) async {
  final container = await StorageTestUtils.createBootstrappedContainer();
  return prepareTestApp(initialLocation: initialLocation, container: container);
}
