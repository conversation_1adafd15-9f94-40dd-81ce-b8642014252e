import 'package:app/core/globals.dart';
import 'package:app/core/services/logger/app_logger.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Utility for setting up storage tests with proper initialization
class StorageTestUtils {
  /// Setup SharedPreferences with mock values
  static void setupSharedPreferences([Map<String, Object>? initialValues]) {
    SharedPreferences.setMockInitialValues(initialValues ?? {});
  }

  /// Create a properly configured ProviderContainer for storage tests
  static ProviderContainer createTestContainer() {
    return ProviderContainer();
  }

  /// Setup bindings required for Flutter tests
  static void setupFlutterBindings() {
    TestWidgetsFlutterBinding.ensureInitialized();
  }

  /// Common setup for storage tests
  static void commonSetup() {
    setupFlutterBindings();
    setupSharedPreferences();

    // Initialize App.config only if not already set (late final can only be set once)
    try {
      // ignore: unnecessary_statements -
      App.config;
      // ignore: avoid_catches_without_on_clauses -
    } catch (e) {
      App.config = AppConfig.test();
    }

    // Initialize logger to prevent late initialization errors
    AppLogger.instance;
  }

  /// Create a container with bootstrapped storage (like in main())
  static Future<ProviderContainer> createBootstrappedContainer() async {
    final bootstrap = ProviderContainer();

    // Initialize storage
    final sharedStore = await bootstrap.read(initializedSharedStorageProvider.future);

    // Create container with overrides
    final container = ProviderContainer(
      overrides: [
        sharedStoreProvider.overrideWithValue(sharedStore),
      ],
    );

    // Dispose bootstrap container
    bootstrap.dispose();

    return container;
  }
}

/// Mixin for storage test groups to reduce boilerplate
mixin StorageTestMixin {
  late ProviderContainer container;

  void setupStorageTest() {
    StorageTestUtils.commonSetup();
    container = StorageTestUtils.createTestContainer();
  }

  void tearDownStorageTest() {
    container.dispose();
  }
}
