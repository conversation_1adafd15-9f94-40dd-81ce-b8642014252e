{"dart.flutterSdkPath": ".fvm/versions/3.32.2", "search.exclude": {"**/.idea": true, "**/.fvm": true, "**/.dart_tool": true, "**/*.freezed.dart": true, "**/*.mocks.dart": true, ".vscode": true, "app.iml": true, "pubspec.lock": true, "**/runner.config.dart": true, "lib/gen": true, ".mason": true}, "files.watcherExclude": {"**/.idea": true, "**/.fvm": true, "**/.dart_tool": true, "**/*.freezed.dart": true, "**/*.mocks.dart": true, ".vscode": true, "app.iml": true, "pubspec.lock": true, "**/runner.config.dart": true, "lib/gen": true, ".mason": true}, "files.exclude": {"**/.idea": true, "**/.fvm": true, "**/.dart_tool": true, "**/*.freezed.dart": true, "**/*.mocks.dart": true, ".vscode": true, "app.iml": true, "pubspec.lock": true, "**/runner.config.dart": true, "lib/gen": true, ".mason": true}, "[dart]": {"editor.rulers": []}, "dart.lineLength": 120, "yaml.schemaStore.enable": false}