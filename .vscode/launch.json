{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Local",
			"request": "launch",
			"type": "dart",
			"program": "lib/main_local.dart",
			"args": [
				"--flavor",
				"local"
			]
		},
		{
			"name": "Local (profile mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "profile",
			"program": "lib/main_local.dart",
			"args": [
				"--flavor",
				"local"
			]
		},
		{
			"name": "Local (release mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "release",
			"program": "lib/main_local.dart",
			"args": [
				"--flavor",
				"local"
			]
		},
		{
			"name": "",
			"request": "launch",
			"type": "node"
		},
		{
			"name": "Dev",
			"request": "launch",
			"type": "dart",
			"program": "lib/main_dev.dart",
			"args": [
				"--flavor",
				"dev"
			]
		},
		{
			"name": "<PERSON> (profile mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "profile",
			"program": "lib/main_dev.dart",
			"args": [
				"--flavor",
				"dev"
			]
		},
		{
			"name": "Dev (release mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "release",
			"program": "lib/main_dev.dart",
			"args": [
				"--flavor",
				"dev"
			]
		},
		{
			"name": "",
			"request": "launch",
			"type": "node"
		},
		{
			"name": "Prod",
			"request": "launch",
			"type": "dart",
			"program": "lib/main_prod.dart",
			"args": [
				"--flavor",
				"prod"
			]
		},
		{
			"name": "Prod (profile mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "profile",
			"program": "lib/main_prod.dart",
			"args": [
				"--flavor",
				"prod"
			]
		},
		{
			"name": "Prod (release mode)",
			"request": "launch",
			"type": "dart",
			"flutterMode": "release",
			"program": "lib/main_prod.dart",
			"args": [
				"--flavor",
				"prod"
			]
		},
	]
}